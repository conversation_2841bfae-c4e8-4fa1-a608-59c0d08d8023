{"info": {"name": "card-lifecycle-event-fulfilment-v1 - Default", "description": "De<PERSON>ult Idempotent Tests", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "healthcheck", "description": "Folder for healthcheck", "item": [{"name": "Get Healthcheck", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["if(responseCode.code === 200){", "", "tests[\"Status code is 200\"] = true;", "", "", "var jsonData = JSON.parse(responseBody);", "tests[\"Status is success\"] = (jsonData.status === \"success\") || (jsonData.type === \"ok\");", "", "}", "else{", "    tests[\"Status Code is: \"+responseCode.code] = false;", "}"]}}], "request": {"auth": {"type": "basic", "basic": [{"key": "username", "value": "{{app-api<PERSON><PERSON>}}", "type": "string"}, {"key": "password", "value": "{{app-apiSecret}}", "type": "string"}, {"key": "saveHelperData", "type": "any"}, {"key": "showPassword", "value": false, "type": "boolean"}]}, "method": "GET", "header": [{"key": "X-appCorrelationId", "value": "{{$guid}}"}, {"key": "X-brandSilo", "value": "WPAC"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{hostname}}/inf/card/lifecycle/event/fulfilment/v1/healthcheck", "host": ["{{hostname}}"], "path": ["inf", "card", "lifecycle", "event", "fulfilment", "v1", "healthcheck"]}}, "response": []}]}]}