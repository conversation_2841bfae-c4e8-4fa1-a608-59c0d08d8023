# API tests Folder Structure

These `api-tests` folder contains all automated API tests that need
to be run against the API.

It contains env folders for env specific tests and the `default/`
folder for tests common to all environments.

It's first use is to store postman collection jobs that are used
in the DevOps CI/CD pipeline to validate that a deployed API works
correctly.

You are encouraged to store all other API tests in the same folder
structure.


## Getting Started

In order to create a postman collection of tests that will be 
automatically run in the DevOps CI/CD pipeline, just create
a postman collection under the env specific folder with the
following name: 

    idempotent_tests_postman_collection.json

Optionally you can use a data file with the following name:

    idempotent_tests_postman_collection_data.json

It will be automatically used to run the postman collection
of tests.


### Prerequisites

Use the Postman Software to create postman collection and data files.
We recommend to use at least Postman version >= 5.2.

### Installing Postman

More Info: 
[Confluence Documentation]
https://confluence.srv.westpac.com.au/display/WDPIPAAS/Test+the+Deployed+API+Using+Postman

## Developing the tests

The tests run by the pipeline will be used to validate the deployment and to help to detect
issues that can be caused by the platform.

While developing your tests, ensure they are reliable and idempotent, i.e. they can be run
several times and return the same results.

## Running the tests

The tests listed in the files `idempotent_tests_postman_collection.json`
will be executed in the CI/CD pipeline:

 * all tests in `default/` folder will always run,
 * all env specific tests will run on those specific envs at deployment
   time only.

We use Newman utility to execute the tests.

## Deployment

Once checked in, these tests will automatically be used in the next deployment.

## Security

Ensure you don't check-in sensitive data in your postman collection or data files.
`api key` and `api secrets` are automatically set when your tests are run in the 
WDP CI/CD pipeline so you don't need to put them.

