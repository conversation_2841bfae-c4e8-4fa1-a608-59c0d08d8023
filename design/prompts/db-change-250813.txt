Based on the current code base and the db design (/design/tables.ddl), now I want to make the following DB changes:
CFRequest.status from NON NULL to NULLABLE, enum remove ASHI_NOTE_ADDED, ASHI_NOTE_ADDITION_FAILED
CFRequest.status enum remove ASHI_NOTE_ADDED
CFRequest.createdDate rename from createdDate to createdDateTime
CFRequest.updateDate rename from updatedDate to updatedDateTime
CFRequestFacilityDetail.facilityId from NON NULL to NULLABLE
CFRequestCustomerDetail.countryCode from NON NULL to NULLABLE
CFRequestCustomerDetail.customerSinceDate from DATETIMEOFFSET to DATE
CFRequestCardDetail.cardEmbossedName from 30 to 50
CFRequestCardDetail.cardActivationDate from DATETIMEOFFSET to DATE
CFRequestCardDetail.cardCreationDate rename from cardCreationDate to cardCreationDateTime


Please do the following: 
1 start from modiying the tabels.ddl, 
2 and then the openapi.yaml, make sure all request and resposne objects are updated.
3 run mvn clean install -Dmaven.test.skip=true
3 update entity class
4 update mappings 
5 update unit test (those json files)
6 update regression test (those .feature files)