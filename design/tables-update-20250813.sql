-- DB Change Script: Apply schema changes for card_digital_service.cf.* tables

-- 1. card_digital_service.cf.CFRequest.status: make nullable
ALTER TABLE card_digital_service.cf.CFRequest ALTER COLUMN status VARCHAR(50) NULL;

-- 2. card_digital_service.cf.CFRequest.createdDate -> createdDateTime (rename)
EXEC sp_rename 'cf.CFRequest.createdDate', 'createdDateTime', 'COLUMN';

-- 3. card_digital_service.cf.CFRequest.updatedDate -> updatedDateTime (rename)
EXEC sp_rename 'cf.CFRequest.updatedDate', 'updatedDateTime', 'COLUMN';

-- 4. card_digital_service.cf.CFRequestFacilityDetail.facilityId: make nullable
ALTER TABLE card_digital_service.cf.CFRequestFacilityDetail ALTER COLUMN facilityId VARCHAR(20) NULL;

-- 5. card_digital_service.cf.CFRequestCustomerDetail.countryCode: make nullable
ALTER TABLE card_digital_service.cf.CFRequestCustomerDetail ALTER COLUMN countryCode VARCHAR(3) NULL;

-- 6. card_digital_service.cf.CFRequestCustomerDetail.customerSinceDateTime (DATETIMEOFFSET) -> customerSinceDate (DATE)
ALTER TABLE card_digital_service.cf.CFRequestCustomerDetail
    ADD customerSinceDate DATE NULL;
UPDATE card_digital_service.cf.CFRequestCustomerDetail
    SET customerSinceDate = CAST(customerSinceDateTime AS DATE);
ALTER TABLE card_digital_service.cf.CFRequestCustomerDetail
    ALTER COLUMN customerSinceDate DATE NOT NULL;
ALTER TABLE card_digital_service.cf.CFRequestCustomerDetail
    DROP COLUMN customerSinceDateTime;

-- 7. card_digital_service.cf.CFRequestCardDetail.cardEmbossedName: increase length from 30 to 50
ALTER TABLE card_digital_service.cf.CFRequestCardDetail ALTER COLUMN cardEmbossedName VARCHAR(50);

-- 8. card_digital_service.cf.CFRequestCardDetail.cardActivationDate: change from DATETIMEOFFSET to DATE
ALTER TABLE card_digital_service.cf.CFRequestCardDetail
    ADD cardActivationDate_tmp DATE NULL;
UPDATE card_digital_service.cf.CFRequestCardDetail
    SET cardActivationDate_tmp = CAST(cardActivationDate AS DATE);
ALTER TABLE card_digital_service.cf.CFRequestCardDetail
    DROP COLUMN cardActivationDate;
EXEC sp_rename 'cf.CFRequestCardDetail.cardActivationDate_tmp', 'cardActivationDate', 'COLUMN';

-- 9. card_digital_service.cf.CFRequestCardDetail.cardCreationDate -> cardCreationDateTime (rename and type change)
ALTER TABLE card_digital_service.cf.CFRequestCardDetail
    ADD cardCreationDateTime DATETIMEOFFSET NULL;
UPDATE card_digital_service.cf.CFRequestCardDetail
    SET cardCreationDateTime = CAST(cardCreationDate AS DATETIMEOFFSET);
ALTER TABLE card_digital_service.cf.CFRequestCardDetail
    DROP COLUMN cardCreationDate;

-- End of DB change script

-- Notes:
-- - Use three-part names for ALTER TABLE (database.schema.table)
-- - Use two-part names for sp_rename (schema.table.column)