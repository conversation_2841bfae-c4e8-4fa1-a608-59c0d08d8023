-- Migration: Move occupation_code and sic_code from CFRequestFacilityDetail to CFRequestCustomerDetail

-- 1. Add columns to CFRequestCustomerDetail
ALTER TABLE [CARD_DIGITAL_SERVICE].[cf].[CFRequestCustomerDetail]
  ADD occupation_code VARCHAR(20),sic_code VARCHAR(20);

-- 2. Copy data from CFRequestFacilityDetail to CFRequestCustomerDetail
-- Assumes both tables have a common key 'request_id'
UPDATE CFRequestCustomerDetail c
JOIN CFRequestFacilityDetail f ON c.request_id = f.request_id
SET
  c.occupation_code = f.occupation_code,
  c.sic_code = f.sic_code;

-- 3. Drop columns from CFRequestFacilityDetail
ALTER TABLE [CARD_DIGITAL_SERVICE].[cf].[CFRequestFacilityDetail]
  DROP COLUMN occupationCode,sicCode;