Endpoint: GET /fulfilmentRequests/{requestId}
Description: Get fulfilment request details in CDS database
Query: brandSilo:
Request Body: None
Response Body: FulfilmentRequestResponse
Response status code: 200 OK

Endpoint: POST /fulfilmentRequests
Description: Creates fulfilment request details in CDS database
Query: brandSilo:
Request Body: Refer to FulfilmentRequestBody
Response Body:
{
 "data": {
   "requestId": "string"
 }
}
Response status code: 201

Endpoint: PUT /fulfilmentRequests/{requestId}
Description: Update fulfilment request details in CDS database
Query: brandSilo:
Request Body: Refer to FulfilmentRequestBody
Response Body: None
Response status code: 204

Endpoint: POST /fulfilmentRequestSearch
Description: Search fulfilment request details from CDS database
Query: brandSilo:
Request Body: Refer to FulfilmentRequestSearchRequest
Response Body: Refer to FulfilmentRequestSearchResponse
Response status code: 200

====================================================
FulfilmentRequestSearchRequest
{
  "type": "string", //length: 25, required: true
  "applicationReferenceNumber": "string", //length: 50, required: false
  "brand": "string", //length: 3, required: true
  "customerId": {// optional
    "id": 12345678,
    "idScheme": "CustomerInternalId"
  },
  "paging": {
    "pageSize": 0,
    "nextPageKey": 0
  }
}

FulfilmentRequestSearchResponse
{
  "data": {
    "requests": [
      $ref=FulfilmentRequestResponseData
    ]
}

FulfilmentRequestBody
{
  "type": "string", //length: 25, required: true
  "status": "string", //length: 50, required: true, enum: FULFILMENTRQST_IDENTIFIER_CREATED, SEQUENCE_NUMBER_GENERATED, SEQUENCE_NUMBER_GENERATION_FAILED, FACILITY_NUMBER_CREATED, FACILITY_NUMBER_CREATION_FAILED, PRODUCT_516_CREATED, PRODUCT_517_CREATED, PRODUCT_516_CREATEATION_FAILED, PRODUCT_517_CREATEATION_FAILED, CARD_ACCOUNT_CREATION_SUCCESS, CARD_ACCOUNT_CREATION_FAILED, CARD_ACCOUNT_PARTIALLY_CREATED, ASHI_NOTE_ADDED, ASHI_NOTE_ADDITION_FAILED, CARD_ARRANGEMENT_CREATION_FAILED, CARD_ARRANGEMENT_CREATION_SUCCESS, CARD_ARRANGEMENT_PARTIALLY_CREATED, COMPLETED
  "previousStatus": "string", //length: 50, required: false, enum: FULFILMENTRQST_IDENTIFIER_CREATED, SEQUENCE_NUMBERS_GENERATED, FACILITY_NUMBER_CREATED, PRODUCT_516_CREATED, PRODUCT_517_CREATED, CARD_ACCOUNT_CREATION_SUCCESS, CARD_ACCOUNT_PARTIALLY_CREATED, ASHI_NOTE_ADDED, CARD_ARRANGEMENT_CREATION_SUCCESS, CARD_ARRANGEMENT_PARTIALLY_CREATED
  "sourceChannel": "string", //length: 5, required: false
  "applicationReferenceNumber": "string", //length: 50, required: true
  "customerDetails": [
    $ref=CustomerDetails
  ],
  "facilityDetails": $ref=FacilityDetails
}

FulfilmentRequestResponse
{
  "data": $ref=FulfilmentRequestResponseData
}

FulfilmentRequestResponseData
{
  "requestId": "string", //required: false
  "type": "string", //length: 25, required: true
  "status": "string", //length: 50, required: true
  "previousStatus": "string", //length: 50, required: false
  "sourceChannel": "string", //length: 5, required: false
  "createdDate": "2025-06-30T05:07:44.741Z", //required: false, pattern: '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{1,3})?(?:Z|[+-]\d{2}:\d{2})?$'
  "createdBy": "string", //length: 50, required: false
  "updatedDate": "2025-06-30T05:07:44.741Z", //required: false, pattern: '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{1,3})?(?:Z|[+-]\d{2}:\d{2})?$'
  "updatedBy": "string", //length: 50, required: false
  "applicationReferenceNumber": "string", //length: 50, required: true
  "customerDetails": [
    $ref=CustomerDetails
  ],
  "facilityDetails": $ref=FacilityDetails
}

CustomerDetails
{
  "cisKey": "string", //length: 11, required: true
  "customerNumber": "string", //length: 10, required: false
  "firstName": "string", //length: 50, required: false
  "middleName": "string", //length: 50, required: false
  "lastName": "string", //length: 50, required: false
  "suffixName": "string", //length: 50, required: false
  "gender": "M" //length: 1, required: false
  "dateOfBirth": "Date" //required: false, pattern: '^\d{4}-\d{2}-\d{2}$'
  "email": "string", //length: 255, required: false
  "alternateEmail": "string", //length: 255, required: false
  "mobilePhoneNumber": "string", //length: 15, required: false
  "workPhoneNumber": "string", //length: 15, required: false
  "homePhoneNumber": "string", //length: 15, required: false
  "countryCode": "str", //length: 3, required: false
  "customerType": "string", //length: 20, required: false
  "customerLinkType": "string", //length: 20, required: false
  "customerStatus": "string", //length: 30, required: false
  "customerSinceDate": "DateTime" //required: false, pattern: '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{1,3})?(?:Z|[+-]\d{2}:\d{2})?$'
  "bankBrand": "string", //length: 3, required: false
  "cardDetails": [
    $ref=CardDetails
  ]
}



CardDetails
{
  "cardNumber": "string", //length: 16, required: true
  "cardExpiry": "string", //length: 8, required: false
  "cardType": "string", //length: 20, required: false
  "cardSubType": "string", //length: 1, required: false
  "cardSuffix": "string", //length: 3, required: false
  "cardScheme": "string", //length: 15, required: false, enum: MASTERCARD, VISA
  "cardStatus": "string" //length: 50, required: false
  "cardEmbossedName": "string" //length: 30, required: false
  "cardActivationDate": "DateTimeOffset" //required: false, pattern: '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{1,3})?(?:Z|[+-]\d{2}:\d{2})?$'
  "cardCreationDate": "DateTimeOffset" //required: false, pattern: '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{1,3})?(?:Z|[+-]\d{2}:\d{2})?$'
  "plasticStockCode": "string" //length: 20, required: false
  "logo": "" //length: 3, required: false
  "cardCreationStatus": "" //length: 50, required: false, enum: CARD_ACCOUNT_CREATION_SUCCESS, CARD_ACCOUNT_CREATION_FAILED
}

FacilityDetails
{
  "facilityId": 0, //length: 20, required: true
  "facilityName": "string", //length: 60, required: false
  "facilityContactName": "string", //length: 60, required: false
  "occupationCode": "string", //length: 10, required: false
  "sicCode": "string", //length: 10, required: false
  "issueCardMailingAddressType": "string", //length: 5, required: false
  "reissueCardMailingAddressType": "string", //length: 5, required: false
  "billingAccountNumber": "string", //length: 20, required: false
  "cardAdminBranchCode": "string", //length: 6, required: false
  "cardCollectionBranchCode": "string", //length: 6, required: false
  "chequeAccountNumber": "string", //length: 16, required: false
  "chequeAccountBSB": "string", //length: 6, required: false
  "addressDetails": [
    $ref=AddressDetails
  ]
}

AddressDetails
{
  "addressType": "string", //length: 50, required: false
  "addressRelationshipInternalKey": "string", //length: 20, required: false
  "addressLine1": "string", //length: 255, required: false
  "addressLine2": "string", //length: 255, required: false
  "addressLine3": "string", //length: 255, required: false
  "city": "string", //length: 50, required: false
  "state": "string", //length: 5, required: false
  "countryCode": "string", //length: 3, required: false
  "postalCode": "string" //length: 10, required: false
}

OrchestrationTrackingEvent
{
  "requestIdentifier": "number", //required: false
  "status": "string", //length: 20, required: false
  "retryCount": "number", //required: false
  "parentWorkflowId": "string", //length: 200, required: false
  "workflowName": "string", //length: 200, required: false
  "workflowId": "string", //length: 200, required: false
  "currentTask": "string", //length: 200, required: false
  "stepId": "string", //length: 200, required: false
  "errorReason": "string", //length: 200, required: false
  "durationInMS": "number" //required: false
}
