---------------------------------------------------------------------------------------------------------
-- DCFIDC-54 : Create Instant Digital Cards (IDC) Database tables
---------------------------------------------------------------------------------------------------------
-- Create the cf schema
CREATE SCHEMA cf;
GO
----------------------------------------------------
PRINT N'Created the cf schema.';
----------------------------------------------------

----------------------------------------
-- DDL for Card Fulfilment (CF) Database
----------------------------------------

----------------------------------------------------
PRINT N'Creating table CFRequest.';
----------------------------------------------------
CREATE TABLE cf.CFRequest
(
    id                            BIGINT IDENTITY(1,1) PRIMARY KEY,                        -- Primary Key: DB generated.
    type                          VARCHAR(25) NOT NULL,                                    -- Request type (e.g., 'cardfacilityorigination', 'lostandstolen').
    status                        VARCHAR(50),                                             -- Request status
    previousStatus                VARCHAR(50),                                             -- Previous request status
    sourceChannel                 VARCHAR(5),                                              -- Source channel of the request (e.g., 'WL', 'MEDB').
    createdDateTime               DATETIMEOFFSET DEFAULT SYSDATETIMEOFFSET() NOT NULL,     -- DateTime (with timezone info) when the record was created
    createdBy                     VARCHAR(50),                                             -- Created By (e.g., 'APP_ID', 'MEDB').
    updatedDateTime               DATETIMEOFFSET DEFAULT SYSDATETIMEOFFSET(),              -- DateTime (with timezone info) when the record was last updated
    updatedBy                     VARCHAR(50),                                             -- Created By (e.g., 'APP_ID', 'STAFF ID').
    applicationReferenceNumber    VARCHAR(50) NOT NULL UNIQUE                              -- Application reference number
);
GO



----------------------------------------------------
PRINT N'Creating table CFRequestFacilityDetail.';
----------------------------------------------------
CREATE TABLE cf.CFRequestFacilityDetail
(
    id                                  BIGINT IDENTITY(1,1) PRIMARY KEY,                -- Primary Key: DB generated.
    cfRequestId                         BIGINT NOT NULL,                                 -- Foreign key that connects to the CFRequestCustomerDetail table.
    facilityId                          VARCHAR(20),                                    -- The facility Id
    facilityName                        VARCHAR(60) NOT NULL,                            -- The facility name
    facilityContactName                 VARCHAR(60),                                     -- Facility contact name
    issueCardMailingAddressType         VARCHAR(5),                                      -- Issue Card Mailing Address Type
    reissueCardMailingAddressType       VARCHAR(5),                                      -- Reissue Card Mailing Address Type
    billingAccountNumber                VARCHAR(20),                                     -- Billing Account Number
    cardAdminBranchCode                 CHAR(6),                                         -- Card Admin Branch Code
    cardCollectionBranchCode            CHAR(6),                                         -- Card Collection Branch Code
    chequeAccountNumber                 VARCHAR(16),                                     -- Cheque Account Number
    chequeAccountBSB                    CHAR(6),                                         -- Cheque Account BSB

    -- Foreign Key Constraint: cfRequestId references the id column in the CFRequest table
    CONSTRAINT FK_CFRequestFacilityDetail_CFRequest FOREIGN KEY (cfRequestId) REFERENCES cf.CFRequest (id)
);
GO


-- Add indexes
----------------------------------------------------
PRINT N'Creating non-clustered index IX_CFRequestFacilityDetail_facilityId.';
----------------------------------------------------
CREATE NONCLUSTERED INDEX IX_CFRequestFacilityDetail_facilityId ON cf.CFRequestFacilityDetail (facilityId);
GO



----------------------------------------------------
PRINT N'Creating table CFRequestAddressDetail.';
----------------------------------------------------
CREATE TABLE cf.CFRequestAddressDetail
(
    id                                  BIGINT IDENTITY(1,1) PRIMARY KEY,                -- Primary Key: DB generated.
    cfRequestFacilityDetailId           BIGINT NOT NULL,                                 -- Foreign key that connects to the CFRequestFacilityDetail table.
    addressType                         VARCHAR(50) NOT NULL,                            -- Address Type
    addressRelationshipInternalKey      VARCHAR(20) NOT NULL,                            -- Address Relationship Internal Key
    addressLine1                        VARCHAR(255) NOT NULL,                           -- Address Line 1
    addressLine2                        VARCHAR(255),                                    -- Address Line 2
    addressLine3                        VARCHAR(255),                                    -- Address Line 3
    city                                VARCHAR(50) NOT NULL,                            -- City
    state                               VARCHAR(5) NOT NULL,                             -- State
    countryCode                         VARCHAR(3) NOT NULL,                             -- Country Code
    postalCode                          VARCHAR(10) NOT NULL,                            -- Postal Code

    -- Foreign Key Constraint: cfRequestFacilityDetailId references the id column in the CFRequestFacilityDetail table
    CONSTRAINT FK_CFRequestAddressDetail_CFRequestFacilityDetail FOREIGN KEY (cfRequestFacilityDetailId) REFERENCES cf.CFRequestFacilityDetail (id)
);
GO


-- Add indexes
----------------------------------------------------
PRINT N'Creating non-clustered index IX_CFRequestAddressDetail_addressRelationshipInternalKey.';
----------------------------------------------------
CREATE NONCLUSTERED INDEX IX_CFRequestAddressDetail_addressRelationshipInternalKey ON cf.CFRequestAddressDetail (addressRelationshipInternalKey);
GO



----------------------------------------------------
PRINT N'Creating table CFRequestCustomerDetail.';
----------------------------------------------------
CREATE TABLE cf.CFRequestCustomerDetail
(
    id                             BIGINT IDENTITY(1,1) PRIMARY KEY,            -- Primary Key: DB generated.
    cfRequestFacilityDetailId      BIGINT NOT NULL,                             -- Foreign key that connects to the CFRequestFacilityDetail table.
    customerId                     VARCHAR(15) NOT NULL,                        -- The customer id (CisKey)
    customerIdScheme               VARCHAR(30) NOT NULL,                        -- The customer id scheme
    firstName                      VARCHAR(50),                                 -- The first name of the customer.
    middleName                     VARCHAR(50),                                 -- The middle name of the customer.
    lastName                       VARCHAR(50),                                 -- The last name of the customer.
    suffixName                     VARCHAR(50),                                 -- The suffix name of the customer.
    gender                         CHAR(1),                                     -- The gender of the customer.
    dateOfBirth                    DATE,                                        -- The date of birth of the customer.
    email                          VARCHAR(255),                                -- The email address of the customer.
    alternateEmail                 VARCHAR(255),                                -- The alternate email address of the customer.
    mobilePhoneNumber              VARCHAR(15),                                 -- The mobile phone number of the customer (to account for international numbers).
    workPhoneNumber                VARCHAR(15),                                 -- The work phone number of the customer (to account for international numbers).
    homePhoneNumber                VARCHAR(15),                                 -- The home phone number of the customer (to account for international numbers).
    countryCode                    VARCHAR(3),                                  -- The country code (e.g., US, GB) indicating the country of the customer.
    customerType                   VARCHAR(20) NOT NULL,                        -- The customer type.
    customerLinkType               VARCHAR(20) NOT NULL,                        -- The customer link type.
    customerStatus                 VARCHAR(30),                                 -- The customer status.
    customerSinceDate              DATE,                                        -- The customer since date.
    bankBrand                      VARCHAR(5) NOT NULL,                         -- The bank brand
    occupationCode                 VARCHAR(10),                                 -- Occupation code (moved from Facility)
    sicCode                        VARCHAR(10),                                 -- Sic Code (moved from Facility)

    -- Foreign Key Constraint: cfRequestFacilityDetailId references the id column in the CFRequestFacilityDetail table
    CONSTRAINT FK_CFRequestCustomerDetail_CFRequestFacilityDetail FOREIGN KEY (cfRequestFacilityDetailId) REFERENCES cf.CFRequestFacilityDetail (id)
);
GO

-- Add indexes
----------------------------------------------------
PRINT N'Creating non-clustered index IX_CFRequestCustomerDetail_customerId.';
----------------------------------------------------
CREATE NONCLUSTERED INDEX IX_CFRequestCustomerDetail_customerId ON cf.CFRequestCustomerDetail (customerId);
GO



----------------------------------------------------
PRINT N'Creating table CFRequestCardDetail.';
----------------------------------------------------
CREATE TABLE cf.CFRequestCardDetail
(
    id                                  BIGINT IDENTITY(1,1) PRIMARY KEY,                -- Primary Key: DB generated.
    cfRequestCustomerDetailId BIGINT    NOT NULL,                                        -- Foreign key that connects to the CFRequestCustomerDetail table.
    cardNumber                          VARCHAR(16) NOT NULL,                            -- The card number.
    cardExpiry                          VARCHAR(8),                                      -- The expiry date of the card (MMYY format).
    cardType                            VARCHAR(20) NOT NULL,                            -- The type of card (e.g., CREDIT, DEBIT).
    cardSubType                         VARCHAR(1),                                      -- The card sub type (e.g., 'B', 'C').
    cardSuffix                          VARCHAR(3),                                      -- The suffix of the card (e.g., 001, 002).
    cardScheme                          VARCHAR(15),                            -- The card scheme (e.g., MASTERCARD, VISA).
    cardStatus                          VARCHAR(50),                                     -- The card status.
    cardEmbossedName                    VARCHAR(50),                                     -- The card embossed name.
    cardActivationDate                  DATE,                                            -- The card activation date.
    cardCreationDateTime                DATETIMEOFFSET,                                  -- The card creation date.
    plasticStockCode                    VARCHAR(20),                                     -- The plastic stock code.
    logo                                CHAR(3) NOT NULL,                                -- The logo
    cardCreationStatus                  VARCHAR(50),                                     -- The creation card status.

    -- Foreign Key Constraint: cfRequestCustomerDetailId references the id column in the CFRequestCustomerDetail table
    CONSTRAINT FK_CFRequestCardDetail_CFRequestCustomerDetail FOREIGN KEY (cfRequestCustomerDetailId) REFERENCES cf.CFRequestCustomerDetail (id)
);
GO


-- Add indexes
----------------------------------------------------
PRINT N'Creating non-clustered index IX_CFRequestCardDetail_cardNumber.';
----------------------------------------------------
CREATE NONCLUSTERED INDEX IX_CFRequestCardDetail_cardNumber ON cf.CFRequestCardDetail (cardNumber);
GO



----------------------------------------------------
PRINT N'Creating table OrchestrationTracking.';
----------------------------------------------------
CREATE TABLE cf.OrchestrationTracking
(
    id                                  BIGINT IDENTITY(1,1) PRIMARY KEY,                -- Primary Key: DB generated.
    cfRequestId                         BIGINT NOT NULL,                                 -- Foreign key that connects to the CFRequest table.
    status                              VARCHAR(20) NOT NULL,                            -- The status
    retryCount                          INT,                                             -- Retry Count
    createdDateTime                     DATETIMEOFFSET DEFAULT SYSDATETIMEOFFSET(),      -- Created Date Time (with timezone info) when the record was created
    updatedDateTime                     DATETIMEOFFSET DEFAULT SYSDATETIMEOFFSET(),      -- Updated Date Time (with timezone info) when the record was last updated
    parentWorkflowId                    VARCHAR(200),                                    -- Parent Workflow Id
    workflowName                        VARCHAR(200) NOT NULL,                           -- Workflow Name
    workflowId                          VARCHAR(200) NOT NULL UNIQUE,                    -- Workflow Id
    currentTask                         VARCHAR(200),                                    -- Current Task
    errorReason                         VARCHAR(200),                                    -- Error Reason
    durationInMS                        DECIMAL,                                         -- Duration in milliseconds

    -- Foreign Key Constraint: cfRequestId references the id column in the CFRequest table
    CONSTRAINT FK_OrchestrationTracking_CFRequest FOREIGN KEY (cfRequestId) REFERENCES cf.CFRequest (id)
);
GO