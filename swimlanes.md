## Virtual Swimlanes
For testing, WDP permits multiple instances of any Component to stand up in parallel and to route traffic between participating
and non-participating components. The platform will schedule additional instances of those Components to run and route
any traffic targeted for that swimlane to the designated version of the Component.

More details can be found here:
https://confluence.srv.westpac.com.au/display/WDPIPAAS/Virtual+Swimlanes

## Quick start guide for using Virtual Swimlanes
Please note that there are costs associated with using Virtual Swimlanes. Contact the WDP Engagement team to discuss:
<EMAIL>

1. Run 'wdp gencode' if you have not done so already. This will populate your pom.xml with the required dependencies.
2. In your master branch, run 'wdp swimlane --enrol' and follow the on screen prompts.
3. This will create a feature branch for you. Create a pull request in Bitbucket and merge the changes.
4. In your swimlane branch, run 'wdp build' and follow the prompts to get a number.
5. Deploy your build using 'wdp deploy --build_number <buildNumber> --swimlane <swimlane>'.

More detailed instructions can be found here:
https://confluence.srv.westpac.com.au/display/WDPIPAAS/Working+with+Virtual+Swimlanes+for+Java+APIs