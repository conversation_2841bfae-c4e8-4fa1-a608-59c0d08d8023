<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>au.com.westpac.card.lifecycle.event.fulfilment.v1</groupId>
    <artifactId>card.lifecycle.event.fulfilment.v1</artifactId>
    <packaging>jar</packaging>
    <name>card.lifecycle.event.fulfilment.v1</name>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <java.version>21</java.version>
        <openapi-generator-version>7.8.0</openapi-generator-version>
    </properties>

    <parent>
        <groupId>au.com.westpac.mesh.commons</groupId>
        <artifactId>springboot-web-parent</artifactId>
        <version>1.0.2</version>
    </parent>

    <dependencies>
<!--        <dependency>-->
<!--            <groupId>au.com.westpac.dcf</groupId>-->
<!--            <artifactId>consumer-finance-commons-lib</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>au.com.westpac.mesh.tenant</groupId>
            <artifactId>consumer-finance-commons</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.36</version>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>12.8.1.jre11</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>2.3.232</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.intuit.karate</groupId>
            <artifactId>karate-junit5</artifactId>
            <version>1.4.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>mockwebserver</artifactId>
            <version>4.12.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
        <plugins>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>7.8.0</version>
                <executions>
                    <execution>
                        <id>generate-api-models</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${basedir}/openapi.yaml</inputSpec>
                            <generatorName>spring</generatorName>
                            <output>${project.build.directory}/generated-sources</output>
                            <generateApis>true</generateApis>
                            <generateApiTests>false</generateApiTests>
                            <generateModels>true</generateModels>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <typeMappings>integer=Long,int=Long</typeMappings>
                            <apiPackage>au.com.westpac.card.lifecycle.event.fulfilment.v1.api</apiPackage>
                            <modelPackage>au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model</modelPackage>
                            <configOptions>
                                <sourceFolder>src/main/java</sourceFolder>
                                <interfaceOnly>true</interfaceOnly>
                                <generateModelTests>true</generateModelTests>
                                <useTags>true</useTags>
                                <dateLibrary>java8</dateLibrary>
                                <serializableModel>true</serializableModel>
                                <useJakartaEe>true</useJakartaEe>
                                <x-nullable>true</x-nullable>
                                <useSpringBoot3>true</useSpringBoot3>
                                <generateBuilders>true</generateBuilders>
                                <reactive>true</reactive>
                            </configOptions>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-core</artifactId>
                        <version>2.18.1</version>
                    </dependency>
                    <dependency>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                        <version>2.18.1</version>
                    </dependency>
                    <dependency>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-annotations</artifactId>
                        <version>2.18.1</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                     <jvmArguments>
                            -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=8000
                            -Xnoagent
                     </jvmArguments>
                     <systemPropertyVariables>
                          <gateway.host>gw.dev1.api.srv.westpac.com.au</gateway.host>
                          <swimlanes.supported>False</swimlanes.supported>
                     </systemPropertyVariables>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>1.9.1</version>
                <executions>
                  <execution>
                    <id>add-source</id>
                    <phase>generate-sources</phase>
                    <goals>
                      <goal>add-source</goal>
                    </goals>
                    <configuration>
                        <sources>
                            <source>${project.build.directory}/generated-sources/src/main/java</source>
                        </sources>
                    </configuration>
                  </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>**/api/**</exclude>
                        <exclude>**/model/**</exclude>
                        <exclude>**/entity/**</exclude>
                        <exclude>**/exception/**</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <id>pre-test</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>generate-code-coverage-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>code-coverage-check</id>
                        <phase>test</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <rule implementation="org.jacoco.maven.RuleConfiguration">
                                    <element>BUNDLE</element>
                                    <limits>
                                        <limit implementation="org.jacoco.report.check.Limit">
                                            <counter>INSTRUCTION</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.20</minimum>
                                        </limit>
                                    </limits>
                                </rule>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.5</version>
                <configuration>
                    <testFailureIgnore>false</testFailureIgnore>
                    <skip>false</skip>
                    <reuseForks>false</reuseForks>
                    <useSystemClassLoader>false</useSystemClassLoader>
                    <excludes>
                        <exclude>regression/**</exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
