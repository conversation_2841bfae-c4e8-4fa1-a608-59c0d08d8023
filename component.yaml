component:
  displayName: Card Lifecycle Event Fulfilment
  description: 
  boundedContext: This API contains operations and resources to retrieve and maintain Card Lifecycle Event Fulfilment.
  componentName: card-lifecycle-event-fulfilment-v1
  componentType: api
  applicationId: A00BD7
  componentCapabilities: [  ]
  contractType: openapi
  userCommunities: [  ]
  brandSiloSupported: [  ]
  # The fully-qualified name of any previous version should be recorded here
  previousVersion: 
  isHidden: false
  # Add some tags to improve the quality of your search results in WDP Connect
  tags: [  ]

  # Change this to 'active' when promoted to Production, 'deprecated' when no new consumers should use this component or 'retired' to mark as end-of-life.
  componentLifecycleState: approved
  
  # The following items should be identical to the parts of componentName
  classificationDomains: [  ]
  shortName: fulfilment
  layer: inf
  majorVersion: 1
