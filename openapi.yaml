openapi: "3.0.3"

################################################################################
#                       API Information                                        #
################################################################################
info:
  title: Card Lifecycle Event Fulfilment API
  description: >
    This API provides operations and resources to retrieve and manage card facility lifecycle event fulfilment.
      1. fulfilmentRequests
         - Create a new card lifecycle event fulfilment request.
      2. fulfilmentRequests/{requestId}
         - Retrieve details of a card lifecycle event     fulfilment request.
      3. fulfilmentRequests/{requestId}
         - Update details of a card lifecycle event fulfilment request.
      4. fulfilmentRequestSearch
         - Search for card lifecycle event fulfilment requests using application reference number and customer ID.
      5. orchestrationTrackingEvents
         - Create an orchestration tracking request.
      6. orchestrationTrackingEvents/{workflowInstanceId}
         - Update an orchestration tracking request by workflowInstanceId.

  version: "1.0.0"
  contact:
    url: "https://mesh.westpac.com.au/components/apis/card-lifecycle-event-fulfilment-v1"
  x-baseSchemaVersion: "0.13.0" # The version of openapi-base on which this specification is based

################################################################################
#                  Base Path                                                   #
################################################################################
servers:
  - url: /inf/card/lifecycle/event/fulfilment/v1
    description: "Base URL for the Card Lifecycle Event Fulfilment API."

################################################################################
#                                  Security                                    #
################################################################################
security:
  - BasicAuth: []


################################################################################
#                      Paths                                                   #
################################################################################
paths:
  /fulfilmentRequests:
    post:
      tags:
        - FulfilmentRequest
      summary: Create a new card facility lifecycle event fulfilment request
      description: Creates a new card lifecycle event fulfilment request
      operationId: createFulfilmentRequest
      parameters:
        - $ref: '#/components/parameters/brandSilo'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FulfilmentRequestBody"
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FulfilmentRequestCreationResponse"
        "400":
          $ref: "#/components/responses/400ErrorResponseWithBodyError"
        "500":
          $ref: "#/components/responses/500ErrorResponse"

  /fulfilmentRequests/{requestId}:
    get:
      tags:
        - FulfilmentRequest
      summary: Get card lifecycle event fulfilment request details
      description: Retrieve card facility lifecycle event fulfilment request details
      operationId: getFulfilmentRequest
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: integer
          description: The ID of the fulfilment request
        - $ref: '#/components/parameters/brandSilo'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FulfilmentRequestResponse"
        "400":
          $ref: "#/components/responses/400ErrorResponseWithPathParameterErrorSample"
        "404":
          $ref: "#/components/responses/404ErrorResponseFulfilmentRequest"
        "500":
          $ref: "#/components/responses/500ErrorResponse"


    put:
      tags:
        - FulfilmentRequest
      summary: Update card lifecycle event fulfilment request details
      description: Update card lifecycle event fulfilment request details
      operationId: updateFulfilmentRequest
      parameters:
        - name: requestId
          in: path
          required: true
          schema:
            type: integer
          description: The ID of the fulfilment request
        - $ref: '#/components/parameters/brandSilo'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FulfilmentRequestBody"
      responses:
        "204":
          description: No Content
        "400":
          $ref: "#/components/responses/400ErrorResponseWithPathPrameterAndBodyErrorSample"
        "404":
          $ref: "#/components/responses/404ErrorResponseFulfilmentRequest"
        "500":
          $ref: "#/components/responses/500ErrorResponse"

  /fulfilmentRequestSearch:
    post:
      tags:
        - FulfilmentRequest
      summary: Search fulfilment request details
      description: Search fulfilment request details using ARN and customerId.
      operationId: searchFulfilmentRequests
      parameters:
        - $ref: '#/components/parameters/brandSilo'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FulfilmentRequestSearchRequest"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FulfilmentRequestSearchResponse"
        "400":
          $ref: "#/components/responses/400ErrorResponseWithBodyError"
        "500":
          $ref: "#/components/responses/500ErrorResponse"

  /orchestrationTrackingEvents:
    post:
      tags:
        - OrchestrationTracking
      summary: Create an orchestration tracking request
      description: This resource provides a restful service for upstream to create an orchestration tracking request.
      operationId: createOrchestrationTrackingEvent
      parameters:
        - $ref: '#/components/parameters/brandSilo'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrchestrationTrackingEvent'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrchestrationTrackingEventCreatedResponse'
              example:
                data:
                  workflowId: "aa02be8e-6536-11f0-99be-5a5b02a5396a"
        "400":
          $ref: "#/components/responses/400ErrorResponseWithBodyError"
        "500":
          $ref: "#/components/responses/500ErrorResponse"

  /orchestrationTrackingEvents/{workflowInstanceId}:
    put:
      tags:
        - OrchestrationTracking
      summary: Update an orchestration tracking request by workflowInstanceId
      description: This resource provides a restful service for upstream to update an orchestration tracking request by workflowInstanceId.
      operationId: updateOrchestrationTrackingEvent
      parameters:
        - name: workflowInstanceId
          in: path
          required: true
          schema:
            type: string
            pattern: '.*'
          description: Unique identifier of the workflow instance
        - $ref: '#/components/parameters/brandSilo'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrchestrationTrackingEvent'
      responses:
        "204":
          description: Updated
        "400":
          $ref: "#/components/responses/400ErrorResponseWithPathPrameterAndBodyErrorSample"
        "404":
          $ref: "#/components/responses/404ErrorResponseOrchestrationTrackingEvent"
        "500":
          $ref: "#/components/responses/500ErrorResponse"

  /healthcheck:
    get:
      description: Retrieve HealthCheck Summary of API.
      operationId: getHealthCheckStatus
      responses:
        "200":
          $ref: "https://schema.mesh.westpac.com.au/platform/contracts/0.13.0/openapi-base.yaml#/components/responses/200healthCheckStatusResponse"

################################################################################
#                           Components                                         #
################################################################################
components:
  securitySchemes:
    BasicAuth:
      type: http
      scheme: basic

  parameters:
    brandSilo:
      name: brandSilo
      in: query
      description: The Brand Silo the caller is operating in. Brand Silo is a concept
        used in Westpac to segregate Customer records based on a combination of the
        organisation that 'owns' the customer relationship and the systems environment
        in which their identity records are created and maintained. A Brand Silo is
        a classification whereas a Brand is a descriptive entity associated with an
        Organisation.
      required: true
      schema:
        type: string
        enum:
          - WPAC

  responses:
    400ErrorResponseWithPathParameterErrorSample:
      description: |
        __Bad Request__

        Possible errors
        
        <table style="text-align: left;">
        <tr>
        <th>Error Code</th>
        <th>Error Message</th>
        <th>Error Description</th>
        </tr>
        <tr>
        <td>1001</td>
        <td>Invalid Header Parameter</td>
        <td>The required {parameter} in header parameter is missing or invalid</td>
        </tr>
        <tr>
        <td>1002</td>
        <td>Invalid Query Parameter</td>
        <td>The required {parameter} in Query parameter is missing or invalid</td>
        </tr>
        <tr>
        <td>1005</td>
        <td>Invalid Path Parameter</td>
        <td>The required {parameter} in Path parameter is missing or invalid</td>
        </tr>        
        </table>

      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            result:
              errors:
                - code: 1002
                  message: 'Invalid Query Parameter'
                  details: 'The required field brandSilo in query parameter is missing or invalid'

    400ErrorResponseWithPathPrameterAndBodyErrorSample:
      description: |
        __Bad Request__

        Possible errors
        
        <table style="text-align: left;">
        <tr>
        <th>Error Code</th>
        <th>Error Message</th>
        <th>Error Description</th>
        </tr>
        <tr>
        <td>1001</td>
        <td>Invalid Header Parameter</td>
        <td>The required {parameter} in header parameter is missing or invalid</td>
        </tr>
        <tr>
        <td>1002</td>
        <td>Invalid Query Parameter</td>
        <td>The required {parameter} in Query parameter is missing or invalid</td>
        </tr>
        <tr>
        <td>1003</td>
        <td>Invalid Body Parameter</td>
        <td>The required {parameter} in Body parameter is missing or invalid</td>
        </tr>
        <tr>
        <td>1005</td>
        <td>Invalid Path Parameter</td>
        <td>The required {parameter} in Path parameter is missing or invalid</td>
        </tr>        
        </table>

      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            result:
              errors:
                - code: 1002
                  message: 'Invalid Query Parameter'
                  details: 'The required field brandSilo in query parameter is missing or invalid'

    400ErrorResponseWithBodyError:
      description: |
        __Bad Request__

        Possible errors

        <table style="text-align: left;">
        <tr>
        <th>Error Code</th>
        <th>Error Message</th>
        <th>Error Description</th>
        </tr>
        <tr>
        <td>1001</td>
        <td>Invalid Header Parameter</td>
        <td>The required {parameter} in header parameter is missing or invalid</td>
        </tr>
        <tr>
        <td>1002</td>
        <td>Invalid Query Parameter</td>
        <td>The required {parameter} in Query parameter is missing or invalid</td>
        </tr>
        <tr>
        <td>1003</td>
        <td>Invalid Body Parameter</td>
        <td>The required {parameter} in Body parameter is missing or invalid</td>
        </tr>
        </table>

      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            result:
              errors:
                - code: 1003
                  message: 'Invalid Body Parameter'
                  details: 'The required field type in Body parameter is missing or invalid'

    404ErrorResponseOrchestrationTrackingEvent:
      description: |
        __Not Found__
        
        Possible errors
        
        <table style="text-align: left;">
        <tr>
        <th>Error Code</th>
        <th>Error Message</th>
        <th>Error Description</th>
        </tr>
        <tr>
        <td>4005</td>
        <td>OrchestrationTrackingEvent not found</td>
        <td>OrchestrationTrackingEvent not found for the provided workflowInstanceId</td>
        </tr>   
        </table>

      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            result:
              errors:
                - code: 4005
                  message: 'OrchestrationTrackingEvent not found'
                  details: 'OrchestrationTrackingEvent not found for the provided workflowInstanceId'

    404ErrorResponseFulfilmentRequest:
      description: |
        __Not Found__
        
        Possible errors
        
        <table style="text-align: left;">
        <tr>
        <th>Error Code</th>
        <th>Error Message</th>
        <th>Error Description</th>
        </tr>
        <tr>
        <td>4005</td>
        <td>Fulfilment request not found</td>
        <td>Fulfilment request was not found for the provide requestid</td>
        </tr>   
        </table>

      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            result:
              errors:
                - code: 4005
                  message: 'Fulfilment request not found'
                  details: 'Fulfilment request was not found for the provide requestid'

    # Server Error Responses
    500ErrorResponse:
      description: |
        __Internal Server Error__

        Possible errors
        
        <table style="text-align: left;">
        <tr>
        <th>Error Code</th>
        <th>Error Message</th>
        <th>Error Description</th>
        </tr>
        <tr>
        <td>10001</td>
        <td>Host Unavailable</td>
        <td>The downstream system failed to accept the request</td>
        </tr>
        <tr>
        <td>10002</td>
        <td>Provider Timeout</td>
        <td>The downstream system failed to respond within the expected time periods</td>
        </tr>
        <tr>
        <td>10003</td>
        <td>Error in Processing</td>
        <td>An unexpected error while processing</td>
        </tr>      
        </table>

      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            result:
              errors:
                - code: 10001
                  message: 'Host Unavailable'
                  details: 'The downstream system failed to accept the request'

  schemas:
    OrchestrationTrackingEventCreatedResponse:
      type: object
      properties:
        data:
          type: object
          description: Orchestration tracking event data
          properties:
            workflowId:
              type: string
              pattern: '.*'
              description: Unique identifier of the workflow instance
              example: "aa02be8e-6536-11f0-99be-5a5b02a5396a"
      required:
        - data
    OrchestrationTrackingEvent:
      type: object
      required:
        - requestIdentifier
        - status
        - workflowName
        - workflowId
      properties:
        requestIdentifier:
          type: integer
          description: Foreign key referencing the CF request
          example: *********
        status:
          type: string
          description: Current status of the orchestration process.
          enum:
            - INPROGRESS
            - COMPLETED
            - FAILURE
            - SUSPENDED
          example: "INPROGRESS"
        retryCount:
          type: integer
          description: Number of retry attempts made for the process (max=4)
          example: 0
        parentWorkflowId:
          type: string
          maxLength: 200
          pattern: '^[A-Za-z0-9-]*$'
          description: Identifier of the parent workflow, if applicable
          example: "aa02be8e-6536-11f0-99be-5a5b02a5396a"
        workflowName:
          type: string
          maxLength: 200
          pattern: '.*'
          description: Name of the orchestration workflow
          example: "card-creation-v1"
        workflowId:
          type: string
          maxLength: 200
          pattern: '.*'
          description: Unique identifier of the workflow instance
          example: "aa02be8e-6536-11f0-99be-5a5b02a5396a"
        currentTask:
          type: string
          maxLength: 200
          pattern: '.*'
          description: Current task
          example: "ValidateInput"
        errorReason:
          type: string
          maxLength: 200
          pattern: '.*'
          description: Error reason
          example: "TimeoutError"
        durationInMS:
          type: number
          description: Duration in milliseconds
          example: 1500
    FulfilmentRequestBody:
      type: object
      required:
        - type
        - applicationReferenceNumber
        - customers
        - facilityDetails
      properties:
        type:
          type: string
          maxLength: 25
          pattern: '.*'
          description: Type of the fulfilment request
          example: "cardfacilityorigination"
        status:
          type: string
          description: Status of the fulfilment request
          enum:
            - FULFILMENTRQST_IDENTIFIER_CREATED
            - SEQUENCE_NUMBERS_GENERATED
            - SEQUENCE_NUMBERS_GENERATION_FAILED
            - FACILITY_NUMBER_CREATED
            - FACILITY_NUMBER_CREATION_FAILED
            - PRODUCT_516_CREATED
            - PRODUCT_517_CREATED
            - PRODUCT_516_CREATION_FAILED
            - PRODUCT_517_CREATION_FAILED
            - FACILITY_ARRANGEMENT_CREATION_SUCCESS
            - FACILITY_ARRANGEMENT_CREATION_FAILED
            - CARD_ACCOUNT_CREATION_SUCCESS
            - CARD_ACCOUNT_CREATION_FAILED
            - CARD_ACCOUNT_PARTIALLY_CREATED
            - CARD_ARRANGEMENT_CREATION_FAILED
            - CARD_ARRANGEMENT_CREATION_SUCCESS
            - CARD_ARRANGEMENT_PARTIALLY_CREATED
            - COMPLETED
          example: "CARD_ACCOUNT_CREATION_SUCCESS"
        previousStatus:
          type: string
          description: Previous status of the fulfilment request
          enum:
            - FULFILMENTRQST_IDENTIFIER_CREATED
            - SEQUENCE_NUMBERS_GENERATED
            - FACILITY_NUMBER_CREATED
            - PRODUCT_516_CREATED
            - PRODUCT_517_CREATED
            - FACILITY_ARRANGEMENT_CREATION_SUCCESS
            - CARD_ACCOUNT_CREATION_SUCCESS
            - CARD_ACCOUNT_PARTIALLY_CREATED
            - CARD_ARRANGEMENT_CREATION_SUCCESS
            - CARD_ARRANGEMENT_PARTIALLY_CREATED
          example: "FACILITY_NUMBER_CREATED"
        sourceChannel:
          type: string
          maxLength: 5
          pattern: '.*'
          description: Source channel
          example: "MEDB"
        createdBy:
          type: string
          maxLength: 50
          pattern: '.*'
          description: Created by (e.g., 'APP_ID', 'MEDB')
          example: "APP_ID"
        updatedBy:
          type: string
          maxLength: 50
          pattern: '.*'
          description: Updated by (e.g., 'APP_ID', 'STAFF ID')
          example: "STAFF_001"
        applicationReferenceNumber:
          type: string
          maxLength: 50
          pattern: '.*'
          description: Application reference number
          example: "*********"
        customers:
          type: array
          description: List of customers
          minItems: 1
          items:
            $ref: '#/components/schemas/CustomerDetails'
        facilityDetails:
          $ref: '#/components/schemas/FacilityDetails'
    FulfilmentRequestSearchRequest:
      type: object
      required:
        - type
      properties:
        type:
          type: string
          maxLength: 25
          pattern: '.*'
          description: Type of the search
          example: "cardfacilityorigination"
        applicationReferenceNumber:
          type: string
          maxLength: 50
          pattern: '.*'
          description: Application reference number
          example: "2024001234"
        brand:
          type: string
          maxLength: 3
          pattern: '.*'
          description: Brand
          example: "WBC"
        customerId:
          $ref: '#/components/schemas/CustomerId'
        paging:
          $ref: '#/components/schemas/Paging'
    Paging:
      type: object
      description: Paging information
      properties:
        pageSize:
          type: integer
          description: Page size
          example: 20
        nextPageKey:
          type: integer
          description: Next page key
          example: 2
    PagingWithCount:
      type: object
      description: Paging information
      properties:
        count:
          type: integer
          description: The count of result
          example: 20
        pageSize:
          type: integer
          description: Page size
          example: 20
        nextPageKey:
          type: integer
          description: Next page key
          example: 2
    CustomerDetails:
      type: object
      required:
        - customerType
        - bankBrand
        - customerId
        - customerLinkType
      properties:
        customerId:
          $ref: '#/components/schemas/CustomerId'
        firstName:
          type: string
          maxLength: 50
          pattern: '.*'
          description: Customer first name
          example: "John"
        middleName:
          type: string
          maxLength: 50
          pattern: '.*'
          description: Customer middle name
          example: "Michael"
        lastName:
          type: string
          maxLength: 50
          pattern: '.*'
          description: Customer last name
          example: "Smith"
        suffixName:
          type: string
          maxLength: 50
          pattern: '.*'
          description: Customer suffix name
          example: "Jr"
        gender:
          type: string
          maxLength: 1
          pattern: '.*'
          description: Customer gender
          example: "M"
        dateOfBirth:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}$'
          description: Customer date of birth (YYYY-MM-DD)
          example: "1985-03-15"
        email:
          type: string
          maxLength: 255
          pattern: '.*'
          description: Customer email address
          example: "<EMAIL>"
        alternateEmail:
          type: string
          maxLength: 255
          pattern: '.*'
          description: Customer alternate email address
          example: "<EMAIL>"
        mobilePhoneNumber:
          type: string
          maxLength: 15
          pattern: '.*'
          description: Customer mobile phone number
          example: "+61412345678"
        workPhoneNumber:
          type: string
          maxLength: 15
          pattern: '.*'
          description: Customer work phone number
          example: "+61287654321"
        homePhoneNumber:
          type: string
          maxLength: 15
          pattern: '.*'
          description: Customer home phone number
          example: "+61298765432"
        countryCode:
          type: string
          maxLength: 3
          pattern: '.*'
          description: Customer country code
          example: "AU"
        customerType:
          type: string
          maxLength: 20
          pattern: '.*'
          description: Customer type
          example: "INDIVIDUAL"
        customerLinkType:
          type: string
          description: Customer link type
          enum:
            - OWNER
            - LINKEDCARDHOLDER
          example: "OWNER"
        customerStatus:
          type: string
          maxLength: 30
          pattern: '.*'
          description: Customer status
          example: "ACTIVE"
        customerSinceDate:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}$'
          description: Customer since date (YYYY-MM-DD)
          example: "2020-01-15"
        bankBrand:
          type: string
          maxLength: 5
          pattern: '.*'
          description: Customer bank brand
          example: "WPAC"
        occupationCode:
          type: string
          maxLength: 10
          pattern: '.*'
          description: Occupation code
          example: "ENG001"
        sicCode:
          type: string
          maxLength: 10
          pattern: '.*'
          description: SIC code
          example: "7372"
        cards:
          type: array
          description: List of cards
          items:
            $ref: '#/components/schemas/CardDetails'
    CardDetails:
      type: object
      required:
        - cardNumber
        - cardType
        - logo
      properties:
        cardNumber:
          type: string
          maxLength: 16
          pattern: '.*'
          description: Card number
          example: "****************"
        cardExpiry:
          type: string
          maxLength: 8
          pattern: '.*'
          description: Card expiry date
          example: "12/26"
        cardType:
          type: string
          maxLength: 20
          pattern: '.*'
          description: Card type
          example: "CREDIT"
        cardSubType:
          type: string
          maxLength: 1
          pattern: '.*'
          description: Card sub type
          example: "B"
        cardSuffix:
          type: string
          maxLength: 3
          pattern: '.*'
          description: Card suffix
          example: "001"
        cardScheme:
          type: string
          description: Card scheme
          enum:
            - MASTERCARD
            - VISA
          example: "VISA"
        cardStatus:
          type: string
          maxLength: 50
          pattern: '.*'
          description: Card status
          example: "ACTIVE"
        cardEmbossedName:
          type: string
          maxLength: 50
          pattern: '.*'
          description: Card embossed name
          example: "JOHN M SMITH"
        cardActivationDate:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}$'
          description: Card activation date (YYYY-MM-DD)
          example: "2024-01-15"
        cardCreationDateTime:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{1,3})?(?:Z|[+-]\d{2}:\d{2})?$'
          description: Card creation date (ISO 8601)
          example: "2024-01-10T14:20:00Z"
        plasticStockCode:
          type: string
          maxLength: 20
          pattern: '.*'
          description: Plastic stock code
          example: "PSCVISAGOLD"
        logo:
          type: string
          maxLength: 3
          pattern: '.*'
          description: Card logo ex:516,517
          example: "516"
        cardCreationStatus:
          type: string
          description: Card creation status
          enum:
            - CARD_ACCOUNT_CREATION_SUCCESS
            - CARD_ACCOUNT_CREATION_FAILED
          example: "CARD_ACCOUNT_CREATION_SUCCESS"
    FacilityDetails:
      description: Facility details.
      required:
        - facilityName
        - addresses
      type: object
      properties:
        facilityId:
          type: string
          maxLength: 20
          pattern: '.*'
          description: Facility ID
          example: "*********"
        facilityName:
          type: string
          maxLength: 60
          pattern: '.*'
          description: Facility name
          example: "Westpac Credit Card Facility"
        facilityContactName:
          type: string
          maxLength: 60
          pattern: '.*'
          description: Facility contact name
          example: "John Smith"
        issueCardMailingAddressType:
          type: string
          maxLength: 5
          pattern: '.*'
          description: Issue card mailing address type
          example: "HOME"
        reissueCardMailingAddressType:
          type: string
          maxLength: 5
          pattern: '.*'
          description: Reissue card mailing address type
          example: "WORK"
        billingAccountNumber:
          type: string
          maxLength: 20
          pattern: '.*'
          description: Billing account number
          example: "2024001"
        cardAdminBranchCode:
          type: string
          maxLength: 6
          pattern: '.*'
          description: Card admin branch code
          example: "032001"
        cardCollectionBranchCode:
          type: string
          maxLength: 6
          pattern: '.*'
          description: Card collection branch code
          example: "032002"
        chequeAccountNumber:
          type: string
          maxLength: 16
          pattern: '.*'
          description: Cheque account number
          example: "1*********123456"
        chequeAccountBSB:
          type: string
          maxLength: 6
          pattern: '.*'
          description: Cheque account BSB
          example: "032001"
        addresses:
          type: array
          description: Address details
          minItems: 1
          items:
            $ref: '#/components/schemas/AddressDetails'
    AddressDetails:
      type: object
      required:
        - addressType
        - addressRelationshipInternalKey
        - addressLine1
        - city
        - state
        - countryCode
        - postalCode
      properties:
        addressType:
          type: string
          maxLength: 50
          pattern: '.*'
          description: Address type i.e facilityAddress or facilityMailingAddress.
          example: "facilityAddress"
        addressRelationshipInternalKey:
          type: string
          maxLength: 20
          pattern: '.*'
          description: Address relationship internal key
          example: "ADDR-REL-001"
        addressLine1:
          type: string
          maxLength: 255
          pattern: '.*'
          description: Address line 1
          example: "123 Collins Street"
        addressLine2:
          type: string
          maxLength: 255
          pattern: '.*'
          description: Address line 2
          example: "Level 15"
        addressLine3:
          type: string
          maxLength: 255
          pattern: '.*'
          description: Address line 3
          example: "Suite 1501"
        city:
          type: string
          maxLength: 50
          pattern: '.*'
          description: City
          example: "Melbourne"
        state:
          type: string
          maxLength: 5
          pattern: '.*'
          description: State
          example: "VIC"
        countryCode:
          type: string
          maxLength: 3
          pattern: '.*'
          description: Country code
          example: "AU"
        postalCode:
          type: string
          maxLength: 10
          pattern: '.*'
          description: Postal code
          example: "3000"
    CustomerId:
      description: Customer id object
      type: object
      required:
        - id
        - idScheme
      properties:
        id:
          description: "CustomerId/CISKey/GCISKey e.g. 12345678."
          type: string
          minLength: 8
          maxLength: 13
          pattern: '^\d+$'
          example: "12345678"
        idScheme:
          type: string
          description: "Scheme of the Id eg:CISKey/CustomerId/GCISKey"
          enum:
            - CustomerInternalId
            - CustomerNumber
          example: "CustomerInternalId"
    ErrorResponse:
      type: object
      properties:
        result:
          $ref: '#/components/schemas/ErrorResult'
      example:
        result:
          errors:
            - code: 10002
              message: 'Provider Timeout'
              details: 'The downstream system failed to respond within the expected time periods'
    ErrorResult:
      type: object
      properties:
        errors:
          type: array
          items:
            $ref: '#/components/schemas/Error'
      example:
        errors:
          - code: 10002
            message: 'Provider Timeout'
            details: 'The downstream system failed to respond within the expected time periods'
    Error:
      description: Represent an individual error.
      type: object
      required:
        - code
        - message
      properties:
        code:
          description: Error Code.
          type: integer
          format: int32
        message:
          description: Error Message
          type: string
        details:
          description: Detailed information about the error
          type: string
      example:
        code: 10002
        message: 'Provider Timeout'
        details: 'The downstream system failed to respond within the expected time periods'
    FulfilmentRequestCreationResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/FulfilmentRequestCreationResponseData'
    FulfilmentRequestCreationResponseData:
      type: object
      properties:
        requestId:
          type: string
          description: The unique identifier for the fulfilment request.
          example: "*********"
    FulfilmentRequestResponseData:
      type: object
      properties:
        requestId:
          type: string
          description: The unique identifier for the fulfilment request.
          example: "*********"
        type:
          type: string
          maxLength: 25
          description: Type of the fulfilment request.
          example: "cardfacilityorigination"
        status:
          type: string
          maxLength: 50
          description: Status of the fulfilment request.
          example: "CARD_ACCOUNT_CREATION_SUCCESS"
        previousStatus:
          type: string
          maxLength: 50
          description: Previous status of the fulfilment request.
          example: "FACILITY_NUMBER_CREATED"
        sourceChannel:
          type: string
          maxLength: 5
          description: Source channel.
          example: "MEDB"
        createdDateTime:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{1,3})?(?:Z|[+-]\d{2}:\d{2})?$'
          description: Date and time when the fulfilment request was created.
          example: "2024-01-15T10:30:00Z"
        createdBy:
          type: string
          maxLength: 50
          description: Who created the fulfilment request.
          example: "SYSTEM_USER"
        updatedDateTime:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{1,3})?(?:Z|[+-]\d{2}:\d{2})?$'
          description: Date and time when the fulfilment request was last updated.
          example: "2024-01-15T14:45:00Z"
        updatedBy:
          type: string
          maxLength: 50
          description: Who last updated the fulfilment request.
          example: "SYSTEM_USER"
        applicationReferenceNumber:
          type: string
          maxLength: 50
          description: Application reference number.
          example: "89001234"
        customers:
          type: array
          items:
            $ref: '#/components/schemas/CustomerDetails'
          description: Customer details.
        facilityDetails:
          $ref: '#/components/schemas/FacilityDetails'
      required:
        - requestId
        - type
        - applicationReferenceNumber
        - customers
        - facilityDetails
    FulfilmentRequestSearchResponse:
      type: object
      properties:
        data:
          type: object
          properties:
            requests:
              type: array
              items:
                $ref: '#/components/schemas/FulfilmentRequestResponseData'
            paging:
              $ref: '#/components/schemas/PagingWithCount'
      required:
        - data
    FulfilmentRequestResponse:
      description: The response containing the card facility fulfilment request details.
      type: object
      properties:
        data:
          $ref: '#/components/schemas/FulfilmentRequestResponseData'
      required:
        - data