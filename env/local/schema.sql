CREATE SCHEMA IF NOT EXISTS cf;

CREATE TABLE IF NOT EXISTS cf.CF_REQUEST
(
    ID                            BIGINT AUTO_INCREMENT PRIMARY KEY,
    TYPE                          VARCHAR(25) NOT NULL,
    STATUS                        VARCHAR(50),
    PREVIOUS_STATUS               VARCHAR(50),
    SOURCE_CHANNEL                VARCHAR(5),
    CREATED_DATE_TIME             TIMESTAMP NOT NULL,
    CREATED_BY                    VARCHAR(50),
    UPDATED_DATE_TIME             TIMESTAMP,
    UPDATED_BY                    VARCHAR(50),
    APPLICATION_REFERENCE_NUMBER  VARCHAR(50) NOT NULL UNIQUE
);

CREATE TABLE IF NOT EXISTS cf.CF_REQUEST_FACILITY_DETAIL
(
    ID                                  BIGINT AUTO_INCREMENT PRIMARY KEY,
    CF_REQUEST_ID                       BIGINT NOT NULL,
    FACILITY_ID                         VARCHAR(20),
    FACILITY_NAME                       VARCHAR(60) NOT NULL,
    FACILITY_CONTACT_NAME               VARCHAR(60),
    ISSUE_CARD_MAILING_ADDRESS_TYPE     VARCHAR(5),
    REISSUE_CARD_MAILING_ADDRESS_TYPE   VARCHAR(5),
    BILLING_ACCOUNT_NUMBER              VARCHAR(20),
    CARD_ADMIN_BRANCH_CODE              VARCHAR(6),
    CARD_COLLECTION_BRANCH_CODE         VARCHAR(6),
    CHEQUE_ACCOUNT_NUMBER               VARCHAR(16),
    CHEQUE_ACCOUNT_BSB                  VARCHAR(6),
    CONSTRAINT FK_CF_REQUEST_FACILITY_DETAIL_CF_REQUEST FOREIGN KEY (CF_REQUEST_ID) REFERENCES cf.CF_REQUEST (ID)
);

CREATE TABLE IF NOT EXISTS cf.CF_REQUEST_ADDRESS_DETAIL
(
    ID                                  BIGINT AUTO_INCREMENT PRIMARY KEY,
    CF_REQUEST_FACILITY_DETAIL_ID       BIGINT NOT NULL,
    ADDRESS_TYPE                        VARCHAR(50) NOT NULL,
    ADDRESS_RELATIONSHIP_INTERNAL_KEY   VARCHAR(20) NOT NULL,
    ADDRESS_LINE_1                      VARCHAR(255) NOT NULL,
    ADDRESS_LINE_2                      VARCHAR(255),
    ADDRESS_LINE_3                      VARCHAR(255),
    CITY                                VARCHAR(50) NOT NULL,
    STATE                               VARCHAR(5) NOT NULL,
    COUNTRY_CODE                        VARCHAR(3) NOT NULL,
    POSTAL_CODE                         VARCHAR(10) NOT NULL,
    CONSTRAINT FK_CF_REQUEST_ADDRESS_DETAIL_CF_REQUEST_FACILITY_DETAIL FOREIGN KEY (CF_REQUEST_FACILITY_DETAIL_ID) REFERENCES cf.CF_REQUEST_FACILITY_DETAIL (ID)
);

CREATE TABLE IF NOT EXISTS cf.CF_REQUEST_CUSTOMER_DETAIL
(
    ID                             BIGINT AUTO_INCREMENT PRIMARY KEY,
    CF_REQUEST_FACILITY_DETAIL_ID  BIGINT NOT NULL,
    CUSTOMER_ID                    VARCHAR(15) NOT NULL,
    CUSTOMER_ID_SCHEME             VARCHAR(30) NOT NULL,
    FIRST_NAME                     VARCHAR(50),
    MIDDLE_NAME                    VARCHAR(50),
    LAST_NAME                      VARCHAR(50),
    SUFFIX_NAME                    VARCHAR(50),
    GENDER                         CHAR(1),
    DATE_OF_BIRTH                  DATE,
    EMAIL                          VARCHAR(255),
    ALTERNATE_EMAIL                VARCHAR(255),
    MOBILE_PHONE_NUMBER            VARCHAR(15),
    WORK_PHONE_NUMBER              VARCHAR(15),
    HOME_PHONE_NUMBER              VARCHAR(15),
    COUNTRY_CODE                   VARCHAR(3),
    CUSTOMER_TYPE                  VARCHAR(20) NOT NULL,
    CUSTOMER_LINK_TYPE             VARCHAR(20) NOT NULL,
    CUSTOMER_STATUS                VARCHAR(30),
    CUSTOMER_SINCE_DATE            DATE,
    BANK_BRAND                     VARCHAR(5) NOT NULL,
    OCCUPATION_CODE                VARCHAR(10),
    SIC_CODE                       VARCHAR(10),
    CONSTRAINT FK_CF_REQUEST_CUSTOMER_DETAIL_CF_REQUEST_FACILITY_DETAIL FOREIGN KEY (CF_REQUEST_FACILITY_DETAIL_ID) REFERENCES cf.CF_REQUEST_FACILITY_DETAIL (ID)
);

CREATE TABLE IF NOT EXISTS cf.CF_REQUEST_CARD_DETAIL
(
    ID                                  BIGINT AUTO_INCREMENT PRIMARY KEY,
    CF_REQUEST_CUSTOMER_DETAIL_ID       BIGINT NOT NULL,
    CARD_NUMBER                         VARCHAR(16) NOT NULL,
    CARD_EXPIRY                         VARCHAR(8),
    CARD_TYPE                           VARCHAR(20) NOT NULL,
    CARD_SUB_TYPE                       CHAR(1),
    CARD_SUFFIX                         VARCHAR(3),
    CARD_SCHEME                         VARCHAR(15),
    CARD_STATUS                         VARCHAR(50),
    CARD_EMBOSSED_NAME                  VARCHAR(50),
    CARD_ACTIVATION_DATE                DATE,
    CARD_CREATION_DATE_TIME             TIMESTAMP,
    PLASTIC_STOCK_CODE                  VARCHAR(20),
    LOGO                                CHAR(3) NOT NULL,
    CARD_CREATION_STATUS                VARCHAR(50),
    CONSTRAINT FK_CF_REQUEST_CARD_DETAIL_CF_REQUEST_CUSTOMER_DETAIL FOREIGN KEY (CF_REQUEST_CUSTOMER_DETAIL_ID) REFERENCES cf.CF_REQUEST_CUSTOMER_DETAIL (ID)
);

CREATE TABLE IF NOT EXISTS cf.ORCHESTRATION_TRACKING
(
    ID                                  BIGINT AUTO_INCREMENT PRIMARY KEY,
    CF_REQUEST_ID                       BIGINT NOT NULL,
    STATUS                              VARCHAR(20) NOT NULL,
    RETRY_COUNT                         INT,
    CREATED_DATE_TIME                   TIMESTAMP,
    UPDATED_DATE_TIME                   TIMESTAMP,
    PARENT_WORKFLOW_ID                  VARCHAR(200),
    WORKFLOW_NAME                       VARCHAR(200) NOT NULL,
    WORKFLOW_ID                         VARCHAR(200) NOT NULL,
    CURRENT_TASK                        VARCHAR(200),
    ERROR_REASON                        VARCHAR(200),
    DURATION_IN_MS                      DECIMAL,
    CONSTRAINT FK_ORCHESTRATION_TRACKING_CF_REQUEST FOREIGN KEY (CF_REQUEST_ID) REFERENCES cf.CF_REQUEST (ID)
);