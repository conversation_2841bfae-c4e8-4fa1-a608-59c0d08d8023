package au.com.westpac.card.lifecycle.event.fulfilment.v1;

import au.com.westpac.mesh.commons.springboot.support.BaseMeshApplication;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.PropertySource;
import org.springframework.lang.NonNull;

import java.sql.SQLException;
import java.util.Arrays;

@SpringBootApplication
@PropertySource("classpath:env.properties")
@PropertySource(value = "classpath:secrets.properties")
@Slf4j
public class Application extends BaseMeshApplication implements org.springframework.context.ApplicationContextAware {

    @Value("${spring.application.name}")
    private String appName;

    private org.springframework.context.ApplicationContext applicationContext;
    static {
        System.setProperty("gateway.host", "gw.dev1.api.srv.westpac.com.au");
    }

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    @PostConstruct
    public void initialize() {
        log.info("Application name: " + appName);
        log.info("Application context id: " + applicationContext.getId());
        log.info("Active profiles: " + Arrays.toString(applicationContext.getEnvironment().getActiveProfiles()));
        log.info("Default profiles: " + Arrays.toString(applicationContext.getEnvironment().getDefaultProfiles()));
        log.info("Java: {} | JVM: {} {} ({})", System.getProperty("java.version"), System.getProperty("java.vm.vendor"), System.getProperty("java.vm.name"), System.getProperty("java.vm.version"));
        log.info("Spring Boot: {}", org.springframework.boot.SpringBootVersion.getVersion());

        try {
            javax.sql.DataSource ds = applicationContext.getBean(javax.sql.DataSource.class);
            try (java.sql.Connection conn = ds.getConnection()) {
                if (!conn.isClosed()) {
                    log.info("HikariCP check: Connection is OPEN");
                } else {
                    log.info("HikariCP check: Connection is CLOSED");
                }
            }
        } catch (SQLException e) {
            log.info("HikariCP check: Error returning database connection: " + e.getMessage());
        }
    }

    @Override
    public void setApplicationContext(@NonNull org.springframework.context.ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }
}