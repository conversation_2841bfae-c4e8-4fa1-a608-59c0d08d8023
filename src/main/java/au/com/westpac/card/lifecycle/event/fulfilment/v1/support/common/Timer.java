package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common;


public class Timer {
        private final long startTime;
        private String source;
        private String dest;

        private boolean fineGrain;

        public static final String EYE_CATCHER_S = "@@@ ";
        public static final String EYE_CATCHER_E = "ms @@@";
        public static final String EXEC_TIME_STRING = "Execution Time taken :: ";

        /**
         * This constructor is used by Coarse
         * grained services
         *
         * @param src - Client
         * @param dst - Service Provider
         */
        public Timer(String src, String dst) {
            startTime = System.currentTimeMillis();
            source = src;
            dest = dst;
        }

        public String toString() {
            StringBuilder s = new StringBuilder(EXEC_TIME_STRING);
            s.append(EYE_CATCHER_S);
            if (!fineGrain) {
                s.append("FROM: ");
                s.append(source);
                s.append(" TO: ");
                s.append(dest);
                s.append(" :ELAPSEDMS: ");
            }
            s.append(System.currentTimeMillis() - startTime);
            s.append(EYE_CATCHER_E);
            return s.toString();
        }

}
