package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity;

import jakarta.persistence.*;
import lombok.*;
import java.time.OffsetDateTime;
import java.util.Objects;

@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Entity
@Table(name = "CF_REQUEST", schema = "cf")
public class CFRequest {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "TYPE", length = 25, nullable = false)
    private String type;

    @Column(name = "STATUS", length = 50)
    private String status;

    @Column(name = "PREVIOUS_STATUS", length = 50)
    private String previousStatus;

    @Column(name = "SOURCE_CHANNEL", length = 5)
    private String sourceChannel;

    @Column(name = "CREATED_DATE_TIME", nullable = false)
    private OffsetDateTime createdDateTime;

    @Column(name = "CREATED_BY", length = 50)
    private String createdBy;

    @Column(name = "UPDATED_DATE_TIME")
    private OffsetDateTime updatedDateTime;

    @Column(name = "UPDATED_BY", length = 50)
    private String updatedBy;

    @Column(name = "APPLICATION_REFERENCE_NUMBER", length = 50, nullable = false, unique = true)
    private String applicationReferenceNumber;

    @OneToOne(mappedBy = "cfRequest", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    @ToString.Exclude
    private CFRequestFacilityDetail facilityDetail;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        CFRequest that = (CFRequest) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }
}