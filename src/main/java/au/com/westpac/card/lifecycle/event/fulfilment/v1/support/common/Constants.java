package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common;

import lombok.experimental.UtilityClass;

@UtilityClass
public class Constants {

    public static final String HEADER_X_MESSAGE_ID = "x-messageId";
    public static final String HEADER_X_APPCORRELATION_ID = "x-appCorrelationId";
    public static final String HEADER_X_ORGANISATION_ID = "x-organisationId";
    public static final String HEADER_X_ORIGINATING_SYSTEM_ID = "x-originatingSystemId";
    public static final String HEADER_X_CONSUMER_TYPE = "x-consumerType";
    public static final String WPAC = "WPAC";
    public static final String API_HEADER_INVALID_EXCEPTION_MESSAGE = "API Header Validation Exception";

    public static final String APP_NAME = "card-lifecycle-event-fulfilment-v1";
}