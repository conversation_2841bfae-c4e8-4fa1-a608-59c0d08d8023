package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common;


import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.Error;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.ErrorResponse;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.ErrorResult;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.biz.fulfilment.FulfilmentNotFoundException;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.biz.orchestration.OrchestrationNotFoundException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.method.annotation.HandlerMethodValidationException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.*;

import static au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common.ApiUtils.logExceptionStacktrace;
import static au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common.Constants.APP_NAME;
import static java.lang.System.in;

@ControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    public static final String INVALID_BODY_PARAMETER = "Invalid Body Parameter";
    public static final String INVALID_HEADER_PARAMETER = "Invalid Header Parameter";
    public static final String BODY_PARAMETER_IS_MISSING_OR_INVALID = "The required %s in body parameter is missing or invalid.";
    public static final String HEADER_PARAMETER_IS_MISSING_OR_INVALID = "The required %s in header parameter is missing or invalid.";
    public static final String INVALID_QUERY_PARAMETER = "Invalid Query Parameter";
    public static final String QUERY_PARAMETER_IS_MISSING_OR_INVALID = "The required %s in query parameter is missing or invalid.";
    public static final String FULFILMENT_REQUEST_NOT_FOUND_MESSAGE = "Fulfilment request not found";
    public static final String FULFILMENT_REQUEST_NOT_FOUND_DETAILS = "Fulfilment request was not found for the provide requestid";
    public static final String ORCHESTRATION_TRACKING_EVENT_NOT_FOUND_MESSAGE = "OrchestrationTrackingEvent not found";
    public static final String ORCHESTRATION_TRACKING_EVENT_NOT_FOUND_DETAILS = "OrchestrationTrackingEvent not found for the provided workflowInstanceId";

    @ExceptionHandler(MissingRequestHeaderException.class)
    protected ResponseEntity<ErrorResponse> handleMissingRequestHeaderException(MissingRequestHeaderException ex) {
        logExceptionStacktrace(logger, ex);
        logger.error(ex.getLocalizedMessage());
        return new ResponseEntity<>(new ErrorResponse().result(new ErrorResult().errors(List.of(new Error()
                .code(1001L)
                .message(INVALID_HEADER_PARAMETER)
                .details(String.format(HEADER_PARAMETER_IS_MISSING_OR_INVALID, ex.getHeaderName()))))), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    protected ResponseEntity<ErrorResponse> handleMissingServletRequestParameterException(MissingServletRequestParameterException ex) {
        logExceptionStacktrace(logger, ex);
        logger.error(ex.getLocalizedMessage());
        return new ResponseEntity<>(new ErrorResponse().result(new ErrorResult().errors(List.of(new Error()
                .code(1002L)
                .message(INVALID_QUERY_PARAMETER)
                .details(QUERY_PARAMETER_IS_MISSING_OR_INVALID.formatted(ex.getParameterName()))))), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    protected ResponseEntity<ErrorResponse> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException ex) {
        logExceptionStacktrace(logger, ex);
        logger.error(ex.getLocalizedMessage());
        return new ResponseEntity<>(new ErrorResponse().result(new ErrorResult().errors(List.of(new Error()
                .code(1002L)
                .message(INVALID_QUERY_PARAMETER)
                .details(QUERY_PARAMETER_IS_MISSING_OR_INVALID.formatted(ex.getName()))))), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    protected ResponseEntity<ErrorResponse> handleHttpMessageNotReadableException(HttpMessageNotReadableException ex) {
        logExceptionStacktrace(logger, ex);
        String message = ex.getCause().toString();
        String field = null;
        int firstBacktick = message.lastIndexOf("[\"");
        int secondBacktick = message.indexOf("\"]", firstBacktick + 1);
        if (firstBacktick != -1 && secondBacktick != -1) {
            field = message.substring(firstBacktick + 2, secondBacktick);
        }
        logger.error(APP_NAME + " Error Message: {}", ex.getLocalizedMessage());
        logger.error(APP_NAME + " Field Errors: {}", field);

        return new ResponseEntity<>(new ErrorResponse().result(new ErrorResult().errors(List.of(new Error()
                .code(1003L)
                .message(INVALID_BODY_PARAMETER)
                .details(BODY_PARAMETER_IS_MISSING_OR_INVALID.formatted(field))))), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    protected ResponseEntity<ErrorResponse> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        logExceptionStacktrace(logger, ex);
        String message = ex.getLocalizedMessage();
    
        List<Error> errors = new ArrayList<>();
        logger.error(APP_NAME + " Field Errors: {}", errors);
        if (message != null) {
            String[] messageArray = message.split("Request'");
            for (String error : messageArray) {
                if (error.contains("on field '")) {
                    int firstBacktick = error.indexOf("'");
                    int secondBacktick = error.indexOf(":");
                    if (firstBacktick != -1 && secondBacktick != -1) {
                        errors.add(new Error()
                                .code(1003L)
                                .message(INVALID_BODY_PARAMETER)
                                .details(BODY_PARAMETER_IS_MISSING_OR_INVALID.formatted(error.substring(firstBacktick + 1, secondBacktick - 1))));
                    }
                }
            }
        }
        return new ResponseEntity<>(new ErrorResponse().result(new ErrorResult().errors(errors)), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(HandlerMethodValidationException.class)
    protected ResponseEntity<ErrorResponse> handleHandlerMethodValidationException(HandlerMethodValidationException ex) {
        logExceptionStacktrace(logger, ex);
        logger.error(APP_NAME + " Error Message: {}", ex.getLocalizedMessage());
        String[] fieldError = Objects.requireNonNull(ex.getAllValidationResults().getFirst().getResolvableErrors().getFirst().getCodes())[0].split("\\.");
        logger.error(APP_NAME + " Field Errors: {}", ex.getAllValidationResults().getFirst().getResolvableErrors());
        String field = fieldError[fieldError.length - 1];
        List<String> headerFields = Arrays.asList("xMessageId", "xAppCorrelationId", "xOrganisationId", "xOriginatingSystemId", "xConsumerType");
        if (headerFields.contains(field)){
            return new ResponseEntity<>(new ErrorResponse().result(new ErrorResult().errors(List.of(new Error()
                    .code(1001L)
                    .message(INVALID_HEADER_PARAMETER)
                    .details(String.format(HEADER_PARAMETER_IS_MISSING_OR_INVALID, field))))), HttpStatus.BAD_REQUEST);
        }
        if ("brandSilo".equals(field)){
            return new ResponseEntity<>(new ErrorResponse().result(new ErrorResult().errors(List.of(new Error()
                    .code(1002L)
                    .message(INVALID_QUERY_PARAMETER)
                    .details(QUERY_PARAMETER_IS_MISSING_OR_INVALID.formatted(field))))), HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(new ErrorResponse().result(new ErrorResult().errors(List.of(new Error()
                .code(1003L)
                .message(INVALID_BODY_PARAMETER)
                .details(BODY_PARAMETER_IS_MISSING_OR_INVALID.formatted(field))))), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    protected ResponseEntity<ErrorResponse> handleConstraintViolationException(ConstraintViolationException ex) {
        logExceptionStacktrace(logger, ex);
        logger.error("Header validation failed ... {}", ex.getLocalizedMessage());
        List<Error> errors = new ArrayList<>();
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        if (violations != null && !violations.isEmpty()) {
            for (ConstraintViolation<?> violation : violations) {
                boolean isDuplicate = errors.stream()
                        .anyMatch(error -> error.getDetails().contains(violation.getPropertyPath().toString()));
                if (!isDuplicate) {
                    errors.add(new Error()
                            .code(1001L)
                            .message(INVALID_HEADER_PARAMETER)
                            .details(HEADER_PARAMETER_IS_MISSING_OR_INVALID
                                    .formatted(violation.getPropertyPath().toString())));
                }
            }
        }
        return new ResponseEntity<>(new ErrorResponse().result(new ErrorResult().errors(errors)), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(FulfilmentNotFoundException.class)
    protected ResponseEntity<ErrorResponse> handleEntityNotFoundExceptionException(FulfilmentNotFoundException ex) {
        logExceptionStacktrace(logger, ex);
        logger.error("Entity not found ... {}", ex.getLocalizedMessage());
        List<Error> errors = List.of(new Error()
                .code(4005L)
                .message(FULFILMENT_REQUEST_NOT_FOUND_MESSAGE)
                .details(FULFILMENT_REQUEST_NOT_FOUND_DETAILS));
        return new ResponseEntity<>(new ErrorResponse().result(new ErrorResult().errors(errors)), HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(OrchestrationNotFoundException.class)
    protected ResponseEntity<ErrorResponse> handleEntityNotFoundExceptionException(OrchestrationNotFoundException ex) {
        logExceptionStacktrace(logger, ex);
        logger.error("Entity not found ... {}", ex.getLocalizedMessage());
        List<Error> errors = List.of(new Error()
                .code(4005L)
                .message(ORCHESTRATION_TRACKING_EVENT_NOT_FOUND_MESSAGE)
                .details(ORCHESTRATION_TRACKING_EVENT_NOT_FOUND_DETAILS));
        return new ResponseEntity<>(new ErrorResponse().result(new ErrorResult().errors(errors)), HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(NoResourceFoundException.class)
    protected ResponseEntity<ErrorResponse> handleNoResourceFoundException(NoResourceFoundException ex) {
        logExceptionStacktrace(logger, ex);
        logger.error(ex.getLocalizedMessage());
        if (ex.getLocalizedMessage().contains("No static resource inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests") || ex.getLocalizedMessage().contains("No static resource inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents")) {
            return new ResponseEntity<>(new ErrorResponse().result(new ErrorResult().errors(List.of(new Error()
                    .code(1005L)
                    .message("Invalid Path Parameter")
                    .details("The required id in Path parameter is missing or invalid.")))), HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(new ErrorResponse().result(new ErrorResult().errors(List.of(new Error()
                .code(4000L)
                .message("Endpoint Not Found")
                .details("The Endpoint is not found.")))), HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(Exception.class)
    protected ResponseEntity<ErrorResponse> handleGeneralException(Exception ex) {
        logExceptionStacktrace(logger, ex);
        logger.error(ex.getLocalizedMessage());
        return new ResponseEntity<>(new ErrorResponse().result(new ErrorResult().errors(List.of(new Error()
                .code(10003L)
                .message("Error in Processing")
                .details("An unexpected error while processing.")))), HttpStatus.INTERNAL_SERVER_ERROR);
    }

}

