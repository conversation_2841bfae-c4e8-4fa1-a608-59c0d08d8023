package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common;


import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Generated;
import lombok.NoArgsConstructor;

import java.lang.Enum;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Generated
public class HeaderRequestDTO {
    @NotEmpty(message = "xMessageId must be provided")
    private String xMessageId;

    @NotEmpty(message = "xAppCorrelationId must be provided")
    private String xAppCorrelationId;

    @NotEmpty(message = "xOrganisationId must be provided")
    private String xOrganisationId;

    @NotEmpty(message = "xOriginatingSystemId must be provided")
    private String xOriginatingSystemId;

    @NotEmpty(message = "xConsumerType must be provided")
    private String xConsumerType;
}
