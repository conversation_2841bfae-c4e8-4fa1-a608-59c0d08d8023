package au.com.westpac.card.lifecycle.event.fulfilment.v1.validator;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import jakarta.annotation.Generated;

/**
 * The Brand Silo the caller is operating in. Brand Silo is a concept used in Westpac to segregate Customer records based on a combination of the organisation that \"owns\" the customer relationship and the systems environment in which their identity records are created and maintained. A Brand Silo is a classification whereas a Brand is a descriptive entity associated with an Organisation
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-07-07T10:19:33.*********+10:00[Australia/Sydney]", comments = "Generator version: 7.8.0")
public enum BrandSiloQueryParam {
  
  WPAC("WPAC");

  private String value;

  BrandSiloQueryParam(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return String.valueOf(value);
  }

  @JsonCreator
  public static BrandSiloQueryParam fromValue(String value) {
    for (BrandSiloQueryParam b : BrandSiloQueryParam.values()) {
      if (b.value.equals(value)) {
        return b;
      }
    }
    throw new IllegalArgumentException("Unexpected value '" + value + "'");
  }
}

