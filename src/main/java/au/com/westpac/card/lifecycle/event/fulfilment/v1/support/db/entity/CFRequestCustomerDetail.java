package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.*;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.LinkedHashSet;
import java.util.Objects;
import java.util.Set;

@Getter
@Setter
@ToString(exclude = {"cfRequestFacilityDetail", "cardDetails"})
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Entity
@Table(name = "CF_REQUEST_CUSTOMER_DETAIL", schema = "cf")
public class CFRequestCustomerDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CF_REQUEST_FACILITY_DETAIL_ID", nullable = false)
    private CFRequestFacilityDetail cfRequestFacilityDetail;

    @Column(name = "CUSTOMER_ID", length = 15, nullable = false)
    private String customerId;

    @Column(name = "CUSTOMER_ID_SCHEME", length = 30, nullable = false)
    private String customerIdScheme;

    @Column(name = "FIRST_NAME", length = 50)
    private String firstName;

    @Column(name = "MIDDLE_NAME", length = 50)
    private String middleName;

    @Column(name = "LAST_NAME", length = 50)
    private String lastName;

    @Column(name = "SUFFIX_NAME", length = 50)
    private String suffixName;

    @Column(name = "GENDER", length = 1, columnDefinition = "CHAR(1)")
    private String gender;

    @Column(name = "DATE_OF_BIRTH")
    private LocalDate dateOfBirth;

    @Column(name = "EMAIL", length = 255)
    private String email;

    @Column(name = "ALTERNATE_EMAIL", length = 255)
    private String alternateEmail;

    @Column(name = "MOBILE_PHONE_NUMBER", length = 15)
    private String mobilePhoneNumber;

    @Column(name = "WORK_PHONE_NUMBER", length = 15)
    private String workPhoneNumber;

    @Column(name = "HOME_PHONE_NUMBER", length = 15)
    private String homePhoneNumber;

    @Column(name = "COUNTRY_CODE", length = 3)
    private String countryCode;

    @Column(name = "CUSTOMER_TYPE", length = 20, nullable = false)
    private String customerType;

    @Column(name = "CUSTOMER_LINK_TYPE", length = 20, nullable = false)
    private String customerLinkType;

    @Column(name = "CUSTOMER_STATUS", length = 30)
    private String customerStatus;

    @Column(name = "CUSTOMER_SINCE_DATE", nullable = false)
    private LocalDate customerSinceDate;

    @Column(name = "BANK_BRAND", length = 5, nullable = false)
    private String bankBrand;

    @Column(name = "OCCUPATION_CODE", length = 10)
    private String occupationCode;

    @Column(name = "SIC_CODE", length = 10)
    private String sicCode;

    @OneToMany(mappedBy = "cfRequestCustomerDetail", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private Set<CFRequestCardDetail> cardDetails = new LinkedHashSet<>();

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        CFRequestCustomerDetail that = (CFRequestCustomerDetail) o;
        return Objects.equals(customerId, that.customerId);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(customerId);
    }
}