package au.com.westpac.card.lifecycle.event.fulfilment.v1.biz.orchestration;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.OrchestrationTrackingEvent;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.OrchestrationTrackingEventCreatedResponse;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.validator.BrandSiloQueryParam;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.validator.XConsumperType;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.validator.XOrganisationId;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
public class OrchestrationTrackingController {

    private final OrchestrationTrackingService orchestrationTrackingService;

    public OrchestrationTrackingController(OrchestrationTrackingService orchestrationTrackingService) {
        this.orchestrationTrackingService = orchestrationTrackingService;
    }

    @PostMapping("/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents")
    @ResponseStatus(HttpStatus.CREATED)
    public OrchestrationTrackingEventCreatedResponse create(
            @NotNull @Parameter(name = "brandSilo", description = "The Brand Silo the caller is operating in. Brand Silo is a concept used in Westpac to segregate Customer records based on a combination of the organisation that 'owns' the customer relationship and the systems environment in which their identity records are created and maintained. A Brand Silo is a classification whereas a Brand is a descriptive entity associated with an Organisation.", required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = "brandSilo", required = true) BrandSiloQueryParam brandSilo,
            @NotNull @Parameter(name = "x-messageId", description = "Must be a GUID (Globally unique message identifier). This uniquely identifies this call. This MUST NOT be propagated", required = true, in = ParameterIn.HEADER) @RequestHeader(value = "x-messageId", required = true) @Size(min = 1) String xMessageId,
            @NotNull @Parameter(name = "x-appCorrelationId", description = "Must be a GUID (Globally unique message identifier). Consumer generated message identifier for correlation purposes. e.g. This is used to group together a number of downstream API/provider calls initiated from a single API call, comprising a business transaction. This MUST be propagated", required = true, in = ParameterIn.HEADER) @RequestHeader(value = "x-appCorrelationId", required = true) @Size(min = 1) String xAppCorrelationId,
            @NotNull @Parameter(name = "x-organisationId", description = "The organisation the caller is operating in.", required = true, in = ParameterIn.HEADER) @RequestHeader(value = "x-organisationId", required = true) XOrganisationId xOrganisationId,
            @NotNull @Parameter(name = "x-originatingSystemId", description = "Alphabet Id of calling application e.g. A00304. Must be propagated to APIs being invoked. Exception for partners calling in via partner APIs, do not populate this header as the partner's consuming application name (eg. ptr-corelogic-valex) will be injected into this field by the gateway.", required = true, in = ParameterIn.HEADER) @RequestHeader(value = "x-originatingSystemId", required = true) @Size(min = 1) String xOriginatingSystemId,
            @NotNull @Parameter(name = "x-consumerType", description = "The originating consumption channel invoking the API (eg. Partner, Staff, Customer). Note that an API consuming another API should pass the value supplied by the calling application. Further information regarding this header can be found at https://confluence.srv.westpac.com.au/x/cbH-B.", required = true, in = ParameterIn.HEADER) @RequestHeader(value = "x-consumerType", required = true) XConsumperType xConsumerType,
            @Valid @RequestBody
            OrchestrationTrackingEvent request
    ) {
        // Delegate mapping and persistence to the service
        return orchestrationTrackingService.create(request);
    }

    @PutMapping("/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/{workflowInstanceId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void update(
            @NotNull @Parameter(name = "brandSilo", description = "The Brand Silo the caller is operating in. Brand Silo is a concept used in Westpac to segregate Customer records based on a combination of the organisation that 'owns' the customer relationship and the systems environment in which their identity records are created and maintained. A Brand Silo is a classification whereas a Brand is a descriptive entity associated with an Organisation.", required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = "brandSilo", required = true) BrandSiloQueryParam brandSilo,
            @NotNull @Parameter(name = "x-messageId", description = "Must be a GUID (Globally unique message identifier). This uniquely identifies this call. This MUST NOT be propagated", required = true, in = ParameterIn.HEADER) @RequestHeader(value = "x-messageId", required = true) @Size(min = 1) String xMessageId,
            @NotNull @Parameter(name = "x-appCorrelationId", description = "Must be a GUID (Globally unique message identifier). Consumer generated message identifier for correlation purposes. e.g. This is used to group together a number of downstream API/provider calls initiated from a single API call, comprising a business transaction. This MUST be propagated", required = true, in = ParameterIn.HEADER) @RequestHeader(value = "x-appCorrelationId", required = true) @Size(min = 1) String xAppCorrelationId,
            @NotNull @Parameter(name = "x-organisationId", description = "The organisation the caller is operating in.", required = true, in = ParameterIn.HEADER) @RequestHeader(value = "x-organisationId", required = true) XOrganisationId xOrganisationId,
            @NotNull @Parameter(name = "x-originatingSystemId", description = "Alphabet Id of calling application e.g. A00304. Must be propagated to APIs being invoked. Exception for partners calling in via partner APIs, do not populate this header as the partner's consuming application name (eg. ptr-corelogic-valex) will be injected into this field by the gateway.", required = true, in = ParameterIn.HEADER) @RequestHeader(value = "x-originatingSystemId", required = true) @Size(min = 1) String xOriginatingSystemId,
            @NotNull @Parameter(name = "x-consumerType", description = "The originating consumption channel invoking the API (eg. Partner, Staff, Customer). Note that an API consuming another API should pass the value supplied by the calling application. Further information regarding this header can be found at https://confluence.srv.westpac.com.au/x/cbH-B.", required = true, in = ParameterIn.HEADER) @RequestHeader(value = "x-consumerType", required = true) XConsumperType xConsumerType,
            @NotNull @Parameter(name = "workflowInstanceId", description = "Unique identifier of the workflow instance", required = true, in = ParameterIn.PATH) @PathVariable(value = "workflowInstanceId", required = true) String workflowInstanceId,
            @Valid @RequestBody
            OrchestrationTrackingEvent request
    ) {
        orchestrationTrackingService.update(workflowInstanceId, request);
    }


}