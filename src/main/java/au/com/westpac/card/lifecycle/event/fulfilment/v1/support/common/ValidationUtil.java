package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.function.Predicate;
import java.util.regex.Pattern;

@Component
@Slf4j
@RequiredArgsConstructor
public class ValidationUtil {

    private final Validator validator;

    public void validateHeaders(HeaderRequestDTO headerRequestDTO) throws ConstraintViolationException {
        log.debug(" Validating the headers {}", headerRequestDTO);
        Set<ConstraintViolation<HeaderRequestDTO>> violations = validator.validate(headerRequestDTO);
        if (!violations.isEmpty()) {
            throw new ConstraintViolationException(Constants.API_HEADER_INVALID_EXCEPTION_MESSAGE, violations);
        }
    }

    private static final String EMAIL_REGEX =
            "(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*" +
                    "|\"(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21\\x23-\\x5b\\x5d-\\x7f]" +
                    "|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])*\")" +
                    "@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?" +
                    "|(?:[0-9]{1,3}\\.){3}[0-9]{1,3}|\\[([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\\])";

    public static final Predicate<String> IS_VALID_EMAIL = email -> Pattern.compile(EMAIL_REGEX, Pattern.CASE_INSENSITIVE).matcher(email).matches();

}