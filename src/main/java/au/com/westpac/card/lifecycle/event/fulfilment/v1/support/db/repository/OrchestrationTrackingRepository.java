// src/main/java/au/com/westpac/card/lifecycle/event/fulfilment/v1/repository/OrchestrationTrackingRepository.java
package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.repository;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.OrchestrationTracking;
import org.springframework.data.jpa.repository.JpaRepository;

public interface OrchestrationTrackingRepository extends JpaRepository<OrchestrationTracking, Long> {
    java.util.Optional<OrchestrationTracking> findByWorkflowId(String workflowId);

    @org.springframework.data.jpa.repository.Modifying
    @org.springframework.data.jpa.repository.Query("""
        UPDATE OrchestrationTracking o SET
            o.cfRequestId = :cfRequestId,
            o.status = :status,
            o.retryCount = :retryCount,
            o.updatedDateTime = :updatedDateTime,
            o.parentWorkflowId = :parentWorkflowId,
            o.workflowName = :workflowName,
            o.currentTask = :currentTask,
            o.errorReason = :errorReason,
            o.durationInMS = :durationInMS
        WHERE o.workflowId = :workflowId
    """)
    int updateAllFieldsByWorkflowId(
        @org.springframework.data.repository.query.Param("workflowId") String workflowId,
        @org.springframework.data.repository.query.Param("cfRequestId") Long cfRequestId,
        @org.springframework.data.repository.query.Param("status") String status,
        @org.springframework.data.repository.query.Param("retryCount") Long retryCount,
        @org.springframework.data.repository.query.Param("updatedDateTime") java.time.OffsetDateTime updatedDateTime,
        @org.springframework.data.repository.query.Param("parentWorkflowId") String parentWorkflowId,
        @org.springframework.data.repository.query.Param("workflowName") String workflowName,
        @org.springframework.data.repository.query.Param("currentTask") String currentTask,
        @org.springframework.data.repository.query.Param("errorReason") String errorReason,
        @org.springframework.data.repository.query.Param("durationInMS") java.math.BigDecimal durationInMS
    );
}
