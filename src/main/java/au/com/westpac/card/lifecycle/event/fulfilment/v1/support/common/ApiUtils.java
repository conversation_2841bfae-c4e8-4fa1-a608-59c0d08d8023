package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;

import java.util.Map;

import static au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common.Constants.APP_NAME;

public class ApiUtils {
    public static ObjectMapper objectMapper = new ObjectMapper();
    static {
        objectMapper.registerModule(new com.fasterxml.jackson.datatype.jsr310.JavaTimeModule());
    }
    public static HeaderRequestDTO buildHeaderRequestDTO(Map<String, String> headerMap) {
        HeaderRequestDTO headerRequestDTO = new HeaderRequestDTO();
        headerRequestDTO.setXMessageId(headerMap.get(Constants.HEADER_X_MESSAGE_ID));
        headerRequestDTO.setXAppCorrelationId(headerMap.get(Constants.HEADER_X_APPCORRELATION_ID));
        headerRequestDTO.setXOrganisationId(headerMap.get(Constants.HEADER_X_ORGANISATION_ID));
        headerRequestDTO.setXOriginatingSystemId(headerMap.get(Constants.HEADER_X_ORIGINATING_SYSTEM_ID));
        headerRequestDTO.setXConsumerType(headerMap.get(Constants.HEADER_X_CONSUMER_TYPE));
        return headerRequestDTO;
    }

    public static String convertObjectToJson(Logger logger, Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            //Just log the error and return null
            logExceptionStacktrace(logger, e);
            return null;
        }
    }

    public static void logExceptionStacktrace(Logger logger, Exception ex) {
        logger.error(APP_NAME + " Error Stacktrace: ", ex);
    }

    public static void logInfo(Logger logger, String message) {
        logger.info(APP_NAME + "Info to attend: {}", message);
    }

    public static void logInfoWithObjectToJson(Logger logger, String message, Object object) {
        String json = convertObjectToJson(logger, object);
        logger.info(APP_NAME + " {} {}", message, json);
    }

    public static void logDebugWithObjectToJson(Logger logger, String message, Object object) {
        String json = convertObjectToJson(logger, object);
        logger.debug(APP_NAME + " {} {}", message, json);
    }

}
