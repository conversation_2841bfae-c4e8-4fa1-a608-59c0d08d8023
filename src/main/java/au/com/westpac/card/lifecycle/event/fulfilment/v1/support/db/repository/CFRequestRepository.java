 // src/main/java/au/com/westpac/card/lifecycle/event/fulfilment/v1/repository/CFRequestRepository.java
package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.repository;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.CFRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.repository.query.QueryByExampleExecutor;

import java.util.List;

public interface CFRequestRepository extends JpaRepository<CFRequest, Long>, QueryByExampleExecutor<CFRequest> {

    /**
     * Search CFRequest by multiple criteria including nested customer details.
     *
     * @param type The request type (optional)
     * @param applicationReferenceNumber The application reference number (optional)
     * @param bankBrand The bank brand from customer details (optional)
     * @param customerId The customer ID from customer details (optional)
     * @return List of matching CFRequest entities
     */
    @Query("SELECT DISTINCT r FROM CFRequest r " +
           "LEFT JOIN r.facilityDetail fd " +
           "LEFT JOIN fd.customerDetails cd " +
           "WHERE (:type IS NULL OR r.type = :type) " +
           "AND (:applicationReferenceNumber IS NULL OR r.applicationReferenceNumber = :applicationReferenceNumber) " +
           "AND (:bankBrand IS NULL OR cd.bankBrand = :bankBrand) " +
           "AND (:customerId IS NULL OR cd.customerId = :customerId)")
    List<CFRequest> findByMultipleCriteria(
            @Param("type") String type,
            @Param("applicationReferenceNumber") String applicationReferenceNumber,
            @Param("bankBrand") String bankBrand,
            @Param("customerId") String customerId
    );

    /**
     * Search CFRequest by multiple criteria with pagination.
     *
     * @param type The request type (optional)
     * @param applicationReferenceNumber The application reference number (optional)
     * @param bankBrand The bank brand from customer details (optional)
     * @param customerId The customer ID from customer details (optional)
     * @param pageable Pagination information
     * @return Page of matching CFRequest entities
     */
    @Query("SELECT DISTINCT r FROM CFRequest r " +
           "LEFT JOIN r.facilityDetail fd " +
           "LEFT JOIN fd.customerDetails cd " +
           "WHERE (:type IS NULL OR r.type = :type) " +
           "AND (:applicationReferenceNumber IS NULL OR r.applicationReferenceNumber = :applicationReferenceNumber) " +
           "AND (:bankBrand IS NULL OR cd.bankBrand = :bankBrand) " +
           "AND (:customerId IS NULL OR cd.customerId = :customerId)")
    Page<CFRequest> findByMultipleCriteria(
            @Param("type") String type,
            @Param("applicationReferenceNumber") String applicationReferenceNumber,
            @Param("bankBrand") String bankBrand,
            @Param("customerId") String customerId,
            Pageable pageable
    );
}
