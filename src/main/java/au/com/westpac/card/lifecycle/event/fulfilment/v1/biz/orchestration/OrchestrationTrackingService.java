package au.com.westpac.card.lifecycle.event.fulfilment.v1.biz.orchestration;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.OrchestrationTrackingEvent;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.OrchestrationTrackingEventCreatedResponse;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.OrchestrationTrackingEventCreatedResponseData;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common.ApiUtils;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.OrchestrationTracking;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.repository.OrchestrationTrackingRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class OrchestrationTrackingService {

    private final OrchestrationTrackingRepository repository;

    public OrchestrationTrackingService(OrchestrationTrackingRepository repository) {
        this.repository = repository;
    }

    public OrchestrationTrackingEventCreatedResponse create(OrchestrationTrackingEvent request) {
        ApiUtils.logInfo(log, "Creating new orchestration tracking event with workflowId: " + request.getWorkflowId());
        ApiUtils.logDebugWithObjectToJson(log, "Create request body:", request);
        OrchestrationTracking entity = mapRequestToEntity(request, null);
        ApiUtils.logDebugWithObjectToJson(log, "Entity to save:", entity);
        OrchestrationTracking saved = repository.save(entity);
        ApiUtils.logInfo(log, "Orchestration tracking event created successfully with id: " + saved.getId());
        return mapEntityToResponse(saved);
    }

    @Transactional
    public void update(String workflowId, OrchestrationTrackingEvent request) {
        ApiUtils.logInfo(log, "Updating orchestration tracking event with workflowId: " + workflowId);
        ApiUtils.logDebugWithObjectToJson(log, "Update request body:", request);
        int updated = repository.updateAllFieldsByWorkflowId(
            workflowId,
            request.getRequestIdentifier(),
            request.getStatus() != null ? request.getStatus().getValue() : null,
            request.getRetryCount(),
            java.time.OffsetDateTime.now(), // updatedDateTime
            request.getParentWorkflowId(),
            request.getWorkflowName(),
            request.getCurrentTask(),
            request.getErrorReason(),
            request.getDurationInMS()
        );
        if (updated == 0) {
            ApiUtils.logInfo(log, "OrchestrationTracking not found for workflowId: " + workflowId);
            throw new OrchestrationNotFoundException("OrchestrationTracking not found for workflowId " + workflowId);
        }
        ApiUtils.logInfo(log, "Update successful for orchestration tracking event with workflowId: " + workflowId);
    }

    private OrchestrationTracking mapRequestToEntity(OrchestrationTrackingEvent request, Long id) {
        OrchestrationTracking.OrchestrationTrackingBuilder builder = OrchestrationTracking.builder();
        builder.id(id);
        builder.cfRequestId(request.getRequestIdentifier());
        builder.status(request.getStatus()!=null ? request.getStatus().getValue():null);
        builder.retryCount(request.getRetryCount()!=null ? request.getRetryCount().intValue():null);
        builder.createdDateTime(java.time.OffsetDateTime.now());
        builder.updatedDateTime(java.time.OffsetDateTime.now());
        builder.parentWorkflowId(request.getParentWorkflowId());
        builder.workflowName(request.getWorkflowName());
        builder.workflowId(request.getWorkflowId());
        builder.currentTask(request.getCurrentTask());
        builder.errorReason(request.getErrorReason());
        builder.durationInMS(request.getDurationInMS());
        return builder.build();
    }

    private OrchestrationTrackingEventCreatedResponse mapEntityToResponse(OrchestrationTracking entity) {
        OrchestrationTrackingEventCreatedResponse response = new OrchestrationTrackingEventCreatedResponse();
        // Map fields as needed
        return OrchestrationTrackingEventCreatedResponse.builder().data(
                OrchestrationTrackingEventCreatedResponseData.builder().workflowId(entity.getId().toString()).build()
        ).build();
    }
}