package au.com.westpac.card.lifecycle.event.fulfilment.v1;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class OpenApiConfig {
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .servers(List.of(new io.swagger.v3.oas.models.servers.Server()
                .url("/inf/card/lifecycle/event/fulfilment/v1")))
            .info(new Info()
                .title("Card Lifecycle Event Fulfilment API")
                .description("Card Lifecycle Event Fulfilment API")
                .version("1.0.0"))
            .addSecurityItem(new io.swagger.v3.oas.models.security.SecurityRequirement().addList("BasicAuth"));
    }
}
