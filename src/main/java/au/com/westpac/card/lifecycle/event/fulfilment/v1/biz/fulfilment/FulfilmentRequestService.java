package au.com.westpac.card.lifecycle.event.fulfilment.v1.biz.fulfilment;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.*;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common.ApiUtils;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.*;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.repository.*;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional
@Slf4j
public class FulfilmentRequestService {

    private static final DateTimeFormatter RFC3339_FORMATTER = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

    private final CFRequestRepository cfRequestRepository;

    public FulfilmentRequestService(CFRequestRepository cfRequestRepository) {
        this.cfRequestRepository = cfRequestRepository;
    }

    public FulfilmentRequestCreationResponse create(FulfilmentRequestBody request) {
        ApiUtils.logInfo(log, "Creating new fulfilment request with application reference number" + request.getApplicationReferenceNumber());
        ApiUtils.logDebugWithObjectToJson(log, "Request body:", request);
        CFRequest entity = mapRequestToEntity(request, null, null);
        ApiUtils.logDebugWithObjectToJson(log, "Entity:", entity);
        CFRequest saved = cfRequestRepository.save(entity);
        ApiUtils.logInfo(log, "Creating new fulfilment request with application reference number" + request.getApplicationReferenceNumber() + " successful with id: " + saved.getId());
        return mapEntityToCreationResponse(saved);
    }

    @Transactional(readOnly = true)
    public FulfilmentRequestResponse getById(Long id) {
        ApiUtils.logInfo(log, "Fetching fulfilment request by id: " + id);
        Optional<CFRequest> entity = cfRequestRepository.findById(id);
        if (entity.isEmpty()) {
            ApiUtils.logInfo(log, "FulfilmentRequest not found for id: " + id);
            throw new FulfilmentNotFoundException("FulfilmentRequest not found for id " + id);
        }
        ApiUtils.logInfo(log, "FulfilmentRequest found for id: " + id);
        return mapEntityToResponse(entity.get());
    }

    public void update(Long id, FulfilmentRequestBody request) {
        ApiUtils.logInfo(log, "Updating fulfilment request with id: " + id);
        ApiUtils.logDebugWithObjectToJson(log, "Update request body:", request);
        CFRequest existingCfRequest = cfRequestRepository.findById(id).orElseThrow(() -> {
            ApiUtils.logInfo(log, "FulfilmentRequest not found for id: " + id);
            return new FulfilmentNotFoundException("FulfilmentRequest not found for id " + id);
        });

        CFRequest entity = mapRequestToEntity(request, id, existingCfRequest);
        ApiUtils.logDebugWithObjectToJson(log, "Entity to update:", entity);
        cfRequestRepository.save(entity);
        ApiUtils.logInfo(log, "Update successful for fulfilment request with id: " + id);
    }

    @Transactional(readOnly = true)
    public FulfilmentRequestSearchResponse search(FulfilmentRequestSearchRequest request) {
        ApiUtils.logInfoWithObjectToJson(log, "Searching fulfilment requests: ", request);
        FulfilmentRequestSearchResponse response = searchWithCustomQuery(request);
        ApiUtils.logInfo(log, "Search completed. Number of results: " +
            (response != null && response.getData() != null && response.getData().getRequests() != null
                ? response.getData().getRequests().size() : 0));
        return response;
    }


    private FulfilmentRequestSearchResponse searchWithCustomQuery(FulfilmentRequestSearchRequest request) {
        // Execute search with pagination if specified
        if (hasPagination(request)) {
            Pageable pageable = createPageable(request);
            Page<CFRequest> page = cfRequestRepository.findByMultipleCriteria(
                    request.getType(),
                    request.getApplicationReferenceNumber(),
                    request.getBrand(),
                    request.getCustomerId()!=null ? request.getCustomerId().getId() : null,
                    pageable);
            return mapPageToSearchResponse(page, request);
        } else {
            // Search without pagination
            List<CFRequest> entities = cfRequestRepository.findByMultipleCriteria(
                    request.getType(),
                    request.getApplicationReferenceNumber(),
                    request.getBrand(),
                    request.getCustomerId()!=null ? request.getCustomerId().getId() : null
            );
            return mapEntitiesToSearchResponse(entities, entities.size(), request);
        }
    }

    private ExampleMatcher createExampleMatcher() {
        return ExampleMatcher.matching()
                .withIgnoreCase() // Case-insensitive matching
                .withIgnoreNullValues() // Ignore null values in example
                .withStringMatcher(ExampleMatcher.StringMatcher.EXACT) // Exact string matching
                .withIgnorePaths("id", "createdDateTime", "updatedDateTime", "createdBy", "updatedBy"); // Ignore audit fields
    }

    private boolean hasPagination(FulfilmentRequestSearchRequest request) {
        return request.getPaging() != null &&
               request.getPaging().getPageSize() != null &&
               request.getPaging().getPageSize() > 0;
    }

    private Pageable createPageable(FulfilmentRequestSearchRequest request) {
        int pageSize = request.getPaging().getPageSize().intValue();
        int pageNumber = 0;

        if (request.getPaging().getNextPageKey() != null && request.getPaging().getNextPageKey() > 0) {
            pageNumber = (int) (request.getPaging().getNextPageKey() / pageSize);
        }

        return PageRequest.of(pageNumber, pageSize);
    }

    private List<CFRequest> applyBrandFilter(List<CFRequest> entities, FulfilmentRequestSearchRequest request) {
        if (request.getBrand() == null || request.getBrand().trim().isEmpty()) {
            return entities; // No brand filter needed
        }

        return entities.stream()
                .filter(entity -> matchesBrandCriteria(entity, request.getBrand()))
                .collect(Collectors.toList());
    }

    private Page<CFRequest> applyBrandFilterToPage(Page<CFRequest> page, FulfilmentRequestSearchRequest request) {
        if (request.getBrand() == null || request.getBrand().trim().isEmpty()) {
            return page; // No brand filter needed
        }

        // Note: For production use, this should be done at the database level with custom queries
        // This is a simplified approach for demonstration
        List<CFRequest> filteredContent = page.getContent().stream()
                .filter(entity -> matchesBrandCriteria(entity, request.getBrand()))
                .toList();

        // Create a new page with filtered content (simplified approach)
        return page; // Return original page for now - in production, use custom repository methods
    }

    private FulfilmentRequestSearchResponse mapPageToSearchResponse(Page<CFRequest> page, FulfilmentRequestSearchRequest request) {
        // Apply brand filter to page content if needed
        List<CFRequest> entities = applyBrandFilter(page.getContent(), request);

        return mapEntitiesToSearchResponse(entities, (int) page.getTotalElements(), request);
    }

    private boolean matchesBrandCriteria(CFRequest entity, String brandCriteria) {
        if (entity.getFacilityDetail() == null ||
            entity.getFacilityDetail().getCustomerDetails() == null ||
            entity.getFacilityDetail().getCustomerDetails().isEmpty()) {
            return false;
        }

        return entity.getFacilityDetail().getCustomerDetails().stream()
                .anyMatch(customer -> customer.getBankBrand() != null &&
                         customer.getBankBrand().equalsIgnoreCase(brandCriteria));
    }

    private CFRequest mapRequestToEntity(FulfilmentRequestBody request, Long id, CFRequest cfRequestExisting) {
        log.debug("Mapping FulfilmentRequestBody to CFRequest entity. Request: {}, Existing entity: {}", request, cfRequestExisting);
        OffsetDateTime now = OffsetDateTime.now();

        CFRequest.CFRequestBuilder builder = CFRequest.builder();
        builder.id(id);
        builder.type(request.getType());
        builder.status(request.getStatus() != null ? request.getStatus().getValue() : null);
        builder.previousStatus(request.getPreviousStatus() != null ? request.getPreviousStatus().getValue() : null);
        builder.sourceChannel(request.getSourceChannel());
        builder.applicationReferenceNumber(request.getApplicationReferenceNumber());

        // Map createdBy and updatedBy from request (new contract)
        if (cfRequestExisting == null) { // Creating new entity
            builder.createdDateTime(now);
            builder.createdBy(request.getCreatedBy());
        } else {
            builder.createdDateTime(cfRequestExisting.getCreatedDateTime());
            builder.createdBy(cfRequestExisting.getCreatedBy());
        }
        builder.updatedDateTime(now);
        builder.updatedBy(request.getUpdatedBy());

        CFRequest cfRequest = builder.build();

        // Map facility details. Facility is always present in the request.
        CFRequestFacilityDetail facilityDetail = mapFacilityDetails(request.getFacilityDetails(), cfRequest);

        if (cfRequestExisting != null) {
            facilityDetail.setId(cfRequestExisting.getFacilityDetail().getId());
        }

        // Map customers (new contract: customers array, minItems: 1)
        Set<CFRequestCustomerDetail> customerEntities = request.getCustomers().stream()
                .map(customer -> mapCustomerDetails(customer, facilityDetail))
                .collect(Collectors.toCollection(HashSet::new));
        facilityDetail.setCustomerDetails(customerEntities);

        cfRequest.setFacilityDetail(facilityDetail);
        log.debug("Mapped CFRequest entity: {}", cfRequest);

        return cfRequest;
    }

    private CFRequestFacilityDetail mapFacilityDetails(FacilityDetails facilityDetails, CFRequest cfRequest) {
        CFRequestFacilityDetail.CFRequestFacilityDetailBuilder builder = CFRequestFacilityDetail.builder();
        builder.cfRequest(cfRequest);
        builder.facilityId(facilityDetails.getFacilityId() != null ? facilityDetails.getFacilityId() : null);
        builder.facilityName(facilityDetails.getFacilityName());
        builder.facilityContactName(facilityDetails.getFacilityContactName());
        builder.issueCardMailingAddressType(facilityDetails.getIssueCardMailingAddressType());
        builder.reissueCardMailingAddressType(facilityDetails.getReissueCardMailingAddressType());
        builder.billingAccountNumber(facilityDetails.getBillingAccountNumber());
        builder.cardAdminBranchCode(facilityDetails.getCardAdminBranchCode());
        builder.cardCollectionBranchCode(facilityDetails.getCardCollectionBranchCode());
        builder.chequeAccountNumber(facilityDetails.getChequeAccountNumber());
        builder.chequeAccountBSB(facilityDetails.getChequeAccountBSB());

        // Build the facility detail first
        CFRequestFacilityDetail facilityDetail = builder.build();

        // Map addresses (new contract: addresses array, always non-empty)
        Set<CFRequestAddressDetail> addressEntities = facilityDetails.getAddresses().stream()
                .map(address -> mapAddressDetails(address, facilityDetail))
                .collect(Collectors.toCollection(HashSet::new));

        // Set the address details on the facility detail
        facilityDetail.setAddressDetails(addressEntities);

        return facilityDetail;
    }

    /**
     * Maps API model AddressDetails to entity CFRequestAddressDetail.
     */
    private CFRequestAddressDetail mapAddressDetails(AddressDetails address, CFRequestFacilityDetail facilityDetail) {
        CFRequestAddressDetail.CFRequestAddressDetailBuilder builder = CFRequestAddressDetail.builder();
        builder.cfRequestFacilityDetail(facilityDetail);
        builder.addressType(address.getAddressType());
        builder.addressRelationshipInternalKey(address.getAddressRelationshipInternalKey());
        builder.addressLine1(address.getAddressLine1());
        builder.addressLine2(address.getAddressLine2());
        builder.addressLine3(address.getAddressLine3());
        builder.city(address.getCity());
        builder.state(address.getState());
        builder.countryCode(address.getCountryCode());
        builder.postalCode(address.getPostalCode());
        return builder.build();
    }

    private FulfilmentRequestCreationResponse mapEntityToCreationResponse(CFRequest entity) {
        FulfilmentRequestCreationResponseData data = FulfilmentRequestCreationResponseData.builder()
                .requestId(entity.getId().toString())
                .build();
        
        return FulfilmentRequestCreationResponse.builder()
                .data(data)
                .build();
    }

    private FulfilmentRequestResponse mapEntityToResponse(CFRequest entity) {
        FulfilmentRequestResponseData data = mapEntityToResponseData(entity);
        
        return FulfilmentRequestResponse.builder()
                .data(data)
                .build();
    }

    private FulfilmentRequestResponseData mapEntityToResponseData(CFRequest entity) {
        FulfilmentRequestResponseData.Builder builder = FulfilmentRequestResponseData.builder();
        builder.requestId(entity.getId().toString());
        builder.type(entity.getType());
        builder.status(entity.getStatus());
        builder.previousStatus(entity.getPreviousStatus());
        builder.sourceChannel(entity.getSourceChannel());
        builder.createdDateTime(entity.getCreatedDateTime() != null ? entity.getCreatedDateTime().format(RFC3339_FORMATTER) : null);
        builder.createdBy(entity.getCreatedBy());
        builder.updatedDateTime(entity.getUpdatedDateTime() != null ? entity.getUpdatedDateTime().format(RFC3339_FORMATTER) : null);
        builder.updatedBy(entity.getUpdatedBy());
        builder.applicationReferenceNumber(entity.getApplicationReferenceNumber());

        // Map facility details if present
        if (entity.getFacilityDetail() != null) {
            builder.facilityDetails(mapEntityToFacilityDetails(entity.getFacilityDetail()));
        }

        // Map customers (new contract: customers array)
        if (entity.getFacilityDetail() != null && entity.getFacilityDetail().getCustomerDetails() != null) {
            List<CustomerDetails> customersList = entity.getFacilityDetail().getCustomerDetails().stream()
                    .map(this::mapEntityToCustomerDetails).toList();
            builder.customers(customersList);
        }

        return builder.build();
    }

    private FacilityDetails mapEntityToFacilityDetails(CFRequestFacilityDetail entity) {
        FacilityDetails.Builder builder = FacilityDetails.builder();
        builder.facilityId(entity.getFacilityId());
        builder.facilityName(entity.getFacilityName());
        builder.facilityContactName(entity.getFacilityContactName());
        builder.issueCardMailingAddressType(entity.getIssueCardMailingAddressType());
        builder.reissueCardMailingAddressType(entity.getReissueCardMailingAddressType());
        builder.billingAccountNumber(entity.getBillingAccountNumber());
        builder.cardAdminBranchCode(entity.getCardAdminBranchCode());
        builder.cardCollectionBranchCode(entity.getCardCollectionBranchCode());
        builder.chequeAccountNumber(entity.getChequeAccountNumber());
        builder.chequeAccountBSB(entity.getChequeAccountBSB());
        builder.addresses(mapEntityToAddressDetails(entity.getAddressDetails()));

        return builder.build();
    }

    private List<AddressDetails> mapEntityToAddressDetails(Set<CFRequestAddressDetail> addressDetails) {
        return addressDetails.stream()
                .map(this::mapEntityToAddressDetail)
                .toList();
    }

    private AddressDetails mapEntityToAddressDetail(CFRequestAddressDetail entity) {
        return AddressDetails.builder()
                .addressType(entity.getAddressType())
                .addressRelationshipInternalKey(entity.getAddressRelationshipInternalKey())
                .addressLine1(entity.getAddressLine1())
                .addressLine2(entity.getAddressLine2())
                .addressLine3(entity.getAddressLine3())
                .city(entity.getCity())
                .state(entity.getState())
                .countryCode(entity.getCountryCode())
                .postalCode(entity.getPostalCode())
                .build();
    }

    private CustomerDetails mapEntityToCustomerDetails(CFRequestCustomerDetail entity) {
        CustomerDetails.Builder builder = CustomerDetails.builder();
        // Map customer details fields
        builder.firstName(entity.getFirstName());
        builder.middleName(entity.getMiddleName());
        builder.lastName(entity.getLastName());
        builder.suffixName(entity.getSuffixName());
        builder.gender(entity.getGender());
        builder.dateOfBirth(entity.getDateOfBirth() != null ? entity.getDateOfBirth().toString() : null); // LocalDate, keep as is (ISO-8601)
        builder.email(entity.getEmail());
        builder.alternateEmail(entity.getAlternateEmail());
        builder.mobilePhoneNumber(entity.getMobilePhoneNumber());
        builder.workPhoneNumber(entity.getWorkPhoneNumber());
        builder.homePhoneNumber(entity.getHomePhoneNumber());
        builder.countryCode(entity.getCountryCode());
        builder.customerType(entity.getCustomerType());
        builder.customerLinkType(CustomerDetails.CustomerLinkTypeEnum.fromValue(entity.getCustomerLinkType()));
        builder.customerStatus(entity.getCustomerStatus());
        builder.customerSinceDate(entity.getCustomerSinceDate() != null ? entity.getCustomerSinceDate().toString() : null);
        builder.bankBrand(entity.getBankBrand());
        builder.occupationCode(entity.getOccupationCode());
        builder.sicCode(entity.getSicCode());

        // Map customer ID
        if (entity.getCustomerId() != null && entity.getCustomerIdScheme() != null) {
            CustomerId customerId = CustomerId.builder()
                    .id(entity.getCustomerId())
                    .idScheme(CustomerId.IdSchemeEnum.fromValue(entity.getCustomerIdScheme()))
                    .build();
            builder.customerId(customerId);
        }

        // Map card details
        if (entity.getCardDetails() != null && !entity.getCardDetails().isEmpty()) {
            List<CardDetails> cardsList = entity.getCardDetails().stream()
                    .map(this::mapEntityToCardDetails)
                    .toList();
            builder.cards(cardsList);
        }

        return builder.build();
    }

    /**
     * Maps entity CFRequestCardDetail to API model CardDetails.
     */
    private CardDetails mapEntityToCardDetails(CFRequestCardDetail entity) {
        CardDetails.Builder builder = CardDetails.builder();
        builder.cardNumber(entity.getCardNumber());
        builder.cardExpiry(entity.getCardExpiry());
        builder.cardType(entity.getCardType());
        builder.cardSubType(entity.getCardSubType());
        builder.cardSuffix(entity.getCardSuffix());

        // Map card scheme enum
        if (entity.getCardScheme() != null) {
            builder.cardScheme(CardDetails.CardSchemeEnum.fromValue(entity.getCardScheme()));
        }

        builder.cardStatus(entity.getCardStatus());
        builder.cardEmbossedName(entity.getCardEmbossedName());

        // Map date fields
        if (entity.getCardActivationDate() != null) {
            builder.cardActivationDate(entity.getCardActivationDate().toString());
        }
        if (entity.getCardCreationDateTime() != null) {
            builder.cardCreationDateTime(entity.getCardCreationDateTime().toString());
        }

        builder.plasticStockCode(entity.getPlasticStockCode());
        builder.logo(entity.getLogo());
        if(entity.getCardCreationStatus() != null) {
            builder.cardCreationStatus(CardDetails.CardCreationStatusEnum.fromValue(entity.getCardCreationStatus()));
        }

        return builder.build();
    }

    /**
     * Maps API model CustomerDetails to entity CFRequestCustomerDetail.
     */
    private CFRequestCustomerDetail mapCustomerDetails(CustomerDetails customer, CFRequestFacilityDetail facilityDetail) {
        CFRequestCustomerDetail.CFRequestCustomerDetailBuilder builder = CFRequestCustomerDetail.builder();
        builder.cfRequestFacilityDetail(facilityDetail);

        // Map customerId and idScheme
        if (customer.getCustomerId() != null) {
            builder.customerId(customer.getCustomerId().getId());
            builder.customerIdScheme(customer.getCustomerId().getIdScheme() != null
                    ? customer.getCustomerId().getIdScheme().toString()
                    : null);
        }

        builder.firstName(customer.getFirstName());
        builder.middleName(customer.getMiddleName());
        builder.lastName(customer.getLastName());
        builder.suffixName(customer.getSuffixName());
        builder.gender(customer.getGender());
        if (customer.getDateOfBirth() != null) {
            builder.dateOfBirth(java.time.LocalDate.parse(customer.getDateOfBirth()));
        }
        builder.email(customer.getEmail());
        builder.alternateEmail(customer.getAlternateEmail());
        builder.mobilePhoneNumber(customer.getMobilePhoneNumber());
        builder.workPhoneNumber(customer.getWorkPhoneNumber());
        builder.homePhoneNumber(customer.getHomePhoneNumber());
        builder.countryCode(customer.getCountryCode());
        builder.customerType(customer.getCustomerType());
        builder.customerLinkType(customer.getCustomerLinkType() != null
                ? customer.getCustomerLinkType().toString()
                : null);
        builder.customerStatus(customer.getCustomerStatus());
        if (customer.getCustomerSinceDate() != null) {
            builder.customerSinceDate(java.time.LocalDate.parse(customer.getCustomerSinceDate()));
        }
        builder.bankBrand(customer.getBankBrand());
        builder.occupationCode(customer.getOccupationCode());
        builder.sicCode(customer.getSicCode());

        CFRequestCustomerDetail cfRequestCustomerDetail = builder.build();

        // TODO: Map cardDetails if needed
        if (customer.getCards() != null) {
            Set<CFRequestCardDetail> cardEntities = customer.getCards().stream()
                    .map(card -> mapCardDetails(card, cfRequestCustomerDetail))
                    .collect(Collectors.toCollection(HashSet::new));
            cfRequestCustomerDetail.setCardDetails(cardEntities);
        }

        return cfRequestCustomerDetail;
    }

    private CFRequestCardDetail mapCardDetails(@Valid CardDetails card, CFRequestCustomerDetail cfRequestCustomerDetail) {
        CFRequestCardDetail.CFRequestCardDetailBuilder builder = CFRequestCardDetail.builder();
        builder.cfRequestCustomerDetail(cfRequestCustomerDetail);
        builder.cardNumber(card.getCardNumber());
        builder.cardExpiry(card.getCardExpiry());
        builder.cardType(card.getCardType());
        builder.cardSubType(card.getCardSubType());
        builder.cardSuffix(card.getCardSuffix());

        // Map card scheme
        if (card.getCardScheme() != null) {
            builder.cardScheme(card.getCardScheme().getValue());
        }

        builder.cardStatus(card.getCardStatus());
        builder.cardEmbossedName(card.getCardEmbossedName());

        // Map date fields
        if (card.getCardActivationDate() != null) {
            builder.cardActivationDate(java.time.LocalDate.parse(card.getCardActivationDate()));
        }
        if (card.getCardCreationDateTime() != null) {
            builder.cardCreationDateTime(OffsetDateTime.parse(card.getCardCreationDateTime()));
        }

        builder.plasticStockCode(card.getPlasticStockCode());
        builder.logo(card.getLogo());
        if (card.getCardCreationStatus() != null) builder.cardCreationStatus(card.getCardCreationStatus().getValue());

        return builder.build();
    }

    private FulfilmentRequestSearchResponse mapEntitiesToSearchResponse(List<CFRequest> entities, int totalCount, FulfilmentRequestSearchRequest request) {
        List<FulfilmentRequestResponseData> requests = entities.stream()
                .map(this::mapEntityToResponseData)
                .toList();
        PagingWithCount paging = null;
        if (hasPagination(request)) {
             paging = new PagingWithCount()
                    .count((long) totalCount)
                    .pageSize(request.getPaging().getPageSize())
                    .nextPageKey(request.getPaging().getNextPageKey() + 1);
        }
        return new FulfilmentRequestSearchResponse().data(new FulfilmentRequestSearchResponseData().requests(requests).paging(paging));
    }
}
