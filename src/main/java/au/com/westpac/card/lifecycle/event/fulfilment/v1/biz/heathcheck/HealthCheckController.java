package au.com.westpac.card.lifecycle.event.fulfilment.v1.biz.heathcheck;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.HealthCheckStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;


@RestController
public class HealthCheckController {

    @RequestMapping(
            method = RequestMethod.GET,
            value = "/inf/card/lifecycle/event/fulfilment/v1/healthcheck",
            produces = { "application/json" }
    )
    public Mono<ResponseEntity<HealthCheckStatus>> getHealthCheckStatus2() {
        HealthCheckStatus successStatus = new HealthCheckStatus();
        successStatus.setStatus(HealthCheckStatus.StatusEnum.SUCCESS);
        return Mono.just(ResponseEntity.ok(successStatus));
    }

}