package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity;

import jakarta.persistence.*;
import lombok.*;
import java.time.OffsetDateTime;
import java.util.Objects;

@Getter
@Setter
@ToString(exclude = "cfRequestCustomerDetail")
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Entity
@Table(name = "CF_REQUEST_CARD_DETAIL", schema = "cf")
public class CFRequestCardDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CF_REQUEST_CUSTOMER_DETAIL_ID", nullable = false)
    private CFRequestCustomerDetail cfRequestCustomerDetail;

    @Column(name = "CARD_NUMBER", length = 16, nullable = false)
    private String cardNumber;

    @Column(name = "CARD_EXPIRY", length = 8)
    private String cardExpiry;

    @Column(name = "CARD_TYPE", length = 20, nullable = false)
    private String cardType;

    @Column(name = "CARD_SUB_TYPE", length = 1, columnDefinition = "CHAR(1)")
    private String cardSubType;

    @Column(name = "CARD_SUFFIX", length = 3)
    private String cardSuffix;

    @Column(name = "CARD_SCHEME", length = 15, nullable = false)
    private String cardScheme;

    @Column(name = "CARD_STATUS", length = 50)
    private String cardStatus;

    @Column(name = "CARD_EMBOSSED_NAME", length = 50)
    private String cardEmbossedName;

    @Column(name = "CARD_ACTIVATION_DATE")
    private java.time.LocalDate cardActivationDate;

    @Column(name = "CARD_CREATION_DATE_TIME")
    private OffsetDateTime cardCreationDateTime;

    @Column(name = "PLASTIC_STOCK_CODE", length = 20)
    private String plasticStockCode;

    @Column(name = "LOGO", length = 3, nullable = false, columnDefinition = "CHAR(3)")
    private String logo;

    @Column(name = "CARD_CREATION_STATUS", length = 50)
    private String cardCreationStatus;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        CFRequestCardDetail that = (CFRequestCardDetail) o;
        return Objects.equals(cardNumber, that.cardNumber) &&
            Objects.equals(cardExpiry, that.cardExpiry) &&
            Objects.equals(cardSuffix, that.cardSuffix);
    }

    @Override
    public int hashCode() {
        return Objects.hash(cardNumber, cardExpiry, cardSuffix);
    }
}