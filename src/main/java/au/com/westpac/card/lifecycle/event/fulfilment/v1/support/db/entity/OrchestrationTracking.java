package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity;

import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Objects;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Entity
@Table(name = "ORCHESTRATION_TRACKING", schema = "cf")
public class OrchestrationTracking {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "CF_REQUEST_ID")
    private Long cfRequestId;

    @Column(name = "STATUS", length = 20)
    private String status;

    @Column(name = "RETRY_COUNT")
    private Integer retryCount;

    @Column(name = "CREATED_DATE_TIME")
    private OffsetDateTime createdDateTime;

    @Column(name = "UPDATED_DATE_TIME")
    private OffsetDateTime updatedDateTime;

    @Column(name = "PARENT_WORKFLOW_ID", length = 200)
    private String parentWorkflowId;

    @Column(name = "WORKFLOW_NAME", length = 200)
    private String workflowName;

    @Column(name = "WORKFLOW_ID", length = 200)
    private String workflowId;

    @Column(name = "CURRENT_TASK", length = 200)
    private String currentTask;

    @Column(name = "ERROR_REASON", length = 200)
    private String errorReason;

    @Column(name = "DURATION_IN_MS", precision = 18, scale = 2)
    private BigDecimal durationInMS;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        OrchestrationTracking that = (OrchestrationTracking) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }
}