package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity;

import jakarta.persistence.*;
import lombok.*;

import java.util.LinkedHashSet;
import java.util.Objects;
import java.util.Set;

@Getter
@Setter
@ToString(exclude = {"cfRequest", "addressDetails", "customerDetails"})
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Entity
@Table(name = "CF_REQUEST_FACILITY_DETAIL", schema = "cf")
public class CFRequestFacilityDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CF_REQUEST_ID", nullable = false, unique = true)
    private CFRequest cfRequest;

    @Column(name = "FACILITY_ID", length = 20)
    private String facilityId;

    @Column(name = "FACILITY_NAME", length = 60, nullable = false)
    private String facilityName;

    @Column(name = "FACILITY_CONTACT_NAME", length = 60)
    private String facilityContactName;

    @Column(name = "ISSUE_CARD_MAILING_ADDRESS_TYPE", length = 5)
    private String issueCardMailingAddressType;

    @Column(name = "REISSUE_CARD_MAILING_ADDRESS_TYPE", length = 5)
    private String reissueCardMailingAddressType;

    @Column(name = "BILLING_ACCOUNT_NUMBER", length = 20)
    private String billingAccountNumber;

    @Column(name = "CARD_ADMIN_BRANCH_CODE", length = 6)
    private String cardAdminBranchCode;

    @Column(name = "CARD_COLLECTION_BRANCH_CODE", length = 6)
    private String cardCollectionBranchCode;

    @Column(name = "CHEQUE_ACCOUNT_NUMBER", length = 16)
    private String chequeAccountNumber;

    @Column(name = "CHEQUE_ACCOUNT_BSB", length = 6)
    private String chequeAccountBSB;

    @OneToMany(mappedBy = "cfRequestFacilityDetail", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private Set<CFRequestAddressDetail> addressDetails = new LinkedHashSet<>();

    @OneToMany(mappedBy = "cfRequestFacilityDetail", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private Set<CFRequestCustomerDetail> customerDetails = new LinkedHashSet<>();

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        CFRequestFacilityDetail that = (CFRequestFacilityDetail) o;
        return Objects.equals(facilityId, that.facilityId);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(facilityId);
    }
}