package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity;

import jakarta.persistence.*;
import lombok.*;
import java.util.Objects;

@Getter
@Setter
@ToString(exclude = "cfRequestFacilityDetail")
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Entity
@Table(name = "CF_REQUEST_ADDRESS_DETAIL", schema = "cf")
public class CFRequestAddressDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CF_REQUEST_FACILITY_DETAIL_ID")
    private CFRequestFacilityDetail cfRequestFacilityDetail;

    @Column(name = "ADDRESS_TYPE", length = 50, nullable = false)
    private String addressType;

    @Column(name = "ADDRESS_RELATIONSHIP_INTERNAL_KEY", length = 20, nullable = false)
    private String addressRelationshipInternalKey;

    @Column(name = "ADDRESS_LINE_1", length = 255, nullable = false)
    private String addressLine1;

    @Column(name = "ADDRESS_LINE_2", length = 255)
    private String addressLine2;

    @Column(name = "ADDRESS_LINE_3", length = 255)
    private String addressLine3;

    @Column(name = "CITY", length = 50, nullable = false)
    private String city;

    @Column(name = "STATE", length = 5, nullable = false)
    private String state;

    @Column(name = "COUNTRY_CODE", length = 3, nullable = false)
    private String countryCode;

    @Column(name = "POSTAL_CODE", length = 10, nullable = false)
    private String postalCode;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        CFRequestAddressDetail that = (CFRequestAddressDetail) o;
        return Objects.equals(addressRelationshipInternalKey, that.addressRelationshipInternalKey);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(addressRelationshipInternalKey);
    }
}