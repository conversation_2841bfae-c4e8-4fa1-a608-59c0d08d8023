package regression.karate;

import com.intuit.karate.junit5.Karate;

class TestSingleFeature {

//    String featurePath = "sit/healthcheck/tc000-healthcheck.feature";
//    String featurePath = "sit/fulfilment/fulfilment-request-create-get-update-get.feature";
//    String featurePath = "sit/fulfilment/fulfilment-request-get-by-id-ok.feature";
//    String featurePath = "sit/fulfilment/fulfilment-request-update-ok.feature";
//    String featurePath = "sit/fulfilment/fulfilment-request-search-ok.feature";
    String featurePath = "sit/fulfilment/fulfilment-request-search-with-pagination-ok.feature";
//    String featurePath = "sit/fulfilment/fulfilment-request-create-invalid-request-body.feature";
//    String featurePath = "sit/fulfilment/orchestration-tracking-create-update-ok.feature";
//    String featurePath = "sit/fulfilment/orchestration-tracking-create-invalid-request-body-workflowName.feature";
//    String featurePath = "sit/fulfilment/orchestration-tracking-create-invalid-request-body-workflowId.feature";
//    String featurePath = "sit/fulfilment/orchestration-tracking-update-non-existing.feature";

    @Karate.Test
    Karate testOne() {
        return Karate.run(featurePath).relativeTo(getClass());
    }
}