package regression.karate;

import com.intuit.karate.junit5.Karate;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestMethodOrder;

@TestMethodOrder(OrderAnnotation.class)
public class TestAllFeatures {

    @Karate.Test
    @Order(1)
    Karate test_Healthcheck() {
        return Karate.run("classpath:regression/karate/sit").relativeTo(getClass());
    }

}