package au.com.westpac.card.lifecycle.event.fulfilment.v1.biz.fulfilment;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.*;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.*;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.repository.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FulfilmentRequestServiceSearchTest {

    @Mock
    private CFRequestRepository cfRequestRepository;

    @Mock
    private CFRequestFacilityDetailRepository facilityDetailRepository;

    @Mock
    private CFRequestCustomerDetailRepository customerDetailRepository;

    @Mock
    private CFRequestAddressDetailRepository addressDetailRepository;

    @Mock
    private CFRequestCardDetailRepository cardDetailRepository;

    @InjectMocks
    private FulfilmentRequestService fulfilmentRequestService;

    private CFRequest testRequest1;
    private CFRequest testRequest2;
    private CFRequestCustomerDetail customerDetail1;
    private CFRequestCustomerDetail customerDetail2;

    @BeforeEach
    void setUp() throws Exception {
        // Load test data from JSON files using IntegrationTestSupport
        testRequest1 = regression.junit.IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/CFRequest1.json",
            CFRequest.class
        );
        testRequest2 = regression.junit.IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/CFRequest2.json",
            CFRequest.class
        );
        // Assign customer details for direct access if needed
        if (testRequest1.getFacilityDetail() != null && testRequest1.getFacilityDetail().getCustomerDetails() != null) {
            customerDetail1 = testRequest1.getFacilityDetail().getCustomerDetails().stream().findFirst().orElse(null);
        }
        if (testRequest2.getFacilityDetail() != null && testRequest2.getFacilityDetail().getCustomerDetails() != null) {
            customerDetail2 = testRequest2.getFacilityDetail().getCustomerDetails().stream().findFirst().orElse(null);
        }
    }

    @Test
    void testSearch_ByTypeApplicationRefBrandAndCustomerId() {
        // Given: Search request with all criteria
        FulfilmentRequestSearchRequest request = new FulfilmentRequestSearchRequest();
        request.setType("cardfacilityorigination");
        request.setApplicationReferenceNumber("APP-REF-2024-001234");
        request.setBrand("WBC");

        CustomerId customerId = new CustomerId();
        customerId.setId("CUST123456789");
        customerId.setIdScheme(CustomerId.IdSchemeEnum.CUSTOMER_INTERNAL_ID);
        request.setCustomerId(customerId);

        // Mock: Repository returns matching request
        when(cfRequestRepository.findByMultipleCriteria(
                eq("cardfacilityorigination"),
                eq("APP-REF-2024-001234"),
                eq("WBC"),
                eq("CUST123456789")
        )).thenReturn(Arrays.asList(testRequest1));

        // When: Execute search
        FulfilmentRequestSearchResponse response = fulfilmentRequestService.search(request);

        // Then: Should find exactly 1 matching request
        assertThat(response).isNotNull();

        // Verify: Custom query method was called with correct parameters
        verify(cfRequestRepository).findByMultipleCriteria(
                "cardfacilityorigination",
                "APP-REF-2024-001234",
                "WBC",
                "CUST123456789"
        );
        verify(cfRequestRepository, never()).findAll(any(Example.class));
    }

    @Test
    void testSearch_ByBrandOnly() {
        // Given: Search request with brand only
        FulfilmentRequestSearchRequest request = new FulfilmentRequestSearchRequest();
        request.setBrand("STG");

        // Mock: Repository returns STG branded request
        when(cfRequestRepository.findByMultipleCriteria(
                isNull(),
                isNull(),
                eq("STG"),
                isNull()
        )).thenReturn(Arrays.asList(testRequest2));

        // When: Execute search
        FulfilmentRequestSearchResponse response = fulfilmentRequestService.search(request);

        // Then: Should find STG branded requests
        assertThat(response).isNotNull();

        // Verify: Custom query method was called for nested criteria
        verify(cfRequestRepository).findByMultipleCriteria(null, null, "STG", null);
        verify(cfRequestRepository, never()).findAll(any(Example.class));
    }

    @Test
    void testSearch_ByCustomerIdOnly() {
        // Given: Search request with customer ID only
        FulfilmentRequestSearchRequest request = new FulfilmentRequestSearchRequest();

        CustomerId customerId = new CustomerId();
        customerId.setId("CUST987654321");
        customerId.setIdScheme(CustomerId.IdSchemeEnum.CUSTOMER_INTERNAL_ID);
        request.setCustomerId(customerId);

        // Mock: Repository returns request for that customer
        when(cfRequestRepository.findByMultipleCriteria(
                isNull(),
                isNull(),
                isNull(),
                eq("CUST987654321")
        )).thenReturn(Arrays.asList(testRequest2));

        // When: Execute search
        FulfilmentRequestSearchResponse response = fulfilmentRequestService.search(request);

        // Then: Should find requests for that customer
        assertThat(response).isNotNull();

        // Verify: Custom query method was called for nested criteria
        verify(cfRequestRepository).findByMultipleCriteria(null, null, null, "CUST987654321");
        verify(cfRequestRepository, never()).findAll(any(Example.class));
    }

    @Test
    void testSearch_WithPagination() {
        // Given: Search request with pagination
        FulfilmentRequestSearchRequest request = new FulfilmentRequestSearchRequest();
        request.setBrand("WBC");

        Paging pagination = new Paging();
        pagination.setPageSize(1L);
        pagination.setNextPageKey(0L);
        request.setPaging(pagination);

        // Mock: Repository returns paginated results
        Page<CFRequest> mockPage = new PageImpl<>(
                Arrays.asList(testRequest1),
                PageRequest.of(0, 1),
                1L
        );
        when(cfRequestRepository.findByMultipleCriteria(
                isNull(),
                isNull(),
                eq("WBC"),
                isNull(),
                any(PageRequest.class)
        )).thenReturn(mockPage);

        // When: Execute search
        FulfilmentRequestSearchResponse response = fulfilmentRequestService.search(request);

        // Then: Should return paginated results
        assertThat(response).isNotNull();

        // Verify: Paginated custom query method was called
        verify(cfRequestRepository).findByMultipleCriteria(
                eq(null),
                eq(null),
                eq("WBC"),
                eq(null),
                any(PageRequest.class)
        );
    }

    @Test
    void testSearch_NoValidCriteria() {
        // Given: Search request with no valid criteria
        FulfilmentRequestSearchRequest request = new FulfilmentRequestSearchRequest();

        // When: Execute search
        FulfilmentRequestSearchResponse response = fulfilmentRequestService.search(request);

        // Then: Should return empty response
        assertThat(response).isNotNull();

        // Verify: No repository methods were called due to invalid criteria
        verify(cfRequestRepository, never()).findAll(any(Example.class));
        verify(cfRequestRepository, never()).findByMultipleCriteria(anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void testSearch_ByTypeAndBrand_UsesCustomQuery() {
        // Given: Search request with type and brand (has nested criteria)
        FulfilmentRequestSearchRequest request = new FulfilmentRequestSearchRequest();
        request.setType("cardfacilityorigination");
        request.setBrand("WBC");

        // Mock: Repository returns matching requests via custom query
        when(cfRequestRepository.findByMultipleCriteria(
                eq("cardfacilityorigination"),
                isNull(),
                eq("WBC"),
                isNull()
        )).thenReturn(Arrays.asList(testRequest1));

        // When: Execute search
        FulfilmentRequestSearchResponse response = fulfilmentRequestService.search(request);

        // Then: Should find matching requests using custom query
        assertThat(response).isNotNull();

        // Verify: Custom query method was used (not JPA Example)
        verify(cfRequestRepository).findByMultipleCriteria("cardfacilityorigination", null, "WBC", null);
        verify(cfRequestRepository, never()).findAll(any(Example.class));
    }
}
