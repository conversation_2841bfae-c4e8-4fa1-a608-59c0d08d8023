package au.com.westpac.card.lifecycle.event.fulfilment.v1;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common.Constants;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common.HeaderRequestDTO;
import org.springframework.http.HttpHeaders;

public class MockData {

    public static final String MESSAGE_ID = "message123";
    public static final String APP_CORRELATION_ID = "appCorrelation123";
    public static final String ORGANISATION_ID = "WPAC";
    public static final String ORIGINATING_SYSTEM_ID = "system123";
    public static final String CONSUMER_TYPE = "Partner";

    public static HeaderRequestDTO getHeaderRequestDTO() {
        HeaderRequestDTO headerRequestDTO = new HeaderRequestDTO();
        headerRequestDTO.setXOrganisationId(ORGANISATION_ID);
        headerRequestDTO.setXConsumerType(CONSUMER_TYPE);
        headerRequestDTO.setXOriginatingSystemId(ORIGINATING_SYSTEM_ID);
        headerRequestDTO.setXAppCorrelationId(APP_CORRELATION_ID);
        headerRequestDTO.setXMessageId(MESSAGE_ID);
        return headerRequestDTO;
    }

    public static HttpHeaders createValidHeaders() {
        HttpHeaders validHeaders = new HttpHeaders();
        validHeaders.add(Constants.HEADER_X_MESSAGE_ID, MESSAGE_ID);
        validHeaders.add(Constants.HEADER_X_APPCORRELATION_ID, APP_CORRELATION_ID);
        validHeaders.add(Constants.HEADER_X_ORGANISATION_ID, ORGANISATION_ID);
        validHeaders.add(Constants.HEADER_X_ORIGINATING_SYSTEM_ID, ORIGINATING_SYSTEM_ID);
        validHeaders.add(Constants.HEADER_X_CONSUMER_TYPE, CONSUMER_TYPE);
        return validHeaders;
    }

    public static HttpHeaders createHeadersMissingXMessageId() {
        HttpHeaders headers = new HttpHeaders();
        // headers.add(Constants.HEADER_X_MESSAGE_ID, MESSAGE_ID);
        headers.add(Constants.HEADER_X_APPCORRELATION_ID, APP_CORRELATION_ID);
        headers.add(Constants.HEADER_X_ORGANISATION_ID, ORGANISATION_ID);
        headers.add(Constants.HEADER_X_ORIGINATING_SYSTEM_ID, ORIGINATING_SYSTEM_ID);
        headers.add(Constants.HEADER_X_CONSUMER_TYPE, CONSUMER_TYPE);
        return headers;
    }

    public static HttpHeaders createHeadersMissingXAppCorrelationId() {
        HttpHeaders headers = new HttpHeaders();
        headers.add(Constants.HEADER_X_MESSAGE_ID, MESSAGE_ID);
        // headers.add(Constants.HEADER_X_APPCORRELATION_ID, APP_CORRELATION_ID);
        headers.add(Constants.HEADER_X_ORGANISATION_ID, ORGANISATION_ID);
        headers.add(Constants.HEADER_X_ORIGINATING_SYSTEM_ID, ORIGINATING_SYSTEM_ID);
        headers.add(Constants.HEADER_X_CONSUMER_TYPE, CONSUMER_TYPE);
        return headers;
    }

    public static HttpHeaders createHeadersMissingXOrganisationId() {
        HttpHeaders headers = new HttpHeaders();
        headers.add(Constants.HEADER_X_MESSAGE_ID, MESSAGE_ID);
        headers.add(Constants.HEADER_X_APPCORRELATION_ID, APP_CORRELATION_ID);
        // headers.add(Constants.HEADER_X_ORGANISATION_ID, ORGANISATION_ID);
        headers.add(Constants.HEADER_X_ORIGINATING_SYSTEM_ID, ORIGINATING_SYSTEM_ID);
        headers.add(Constants.HEADER_X_CONSUMER_TYPE, CONSUMER_TYPE);
        return headers;
    }

    public static HttpHeaders createHeadersMissingXOriginatingSystemId() {
        HttpHeaders headers = new HttpHeaders();
        headers.add(Constants.HEADER_X_MESSAGE_ID, MESSAGE_ID);
        headers.add(Constants.HEADER_X_APPCORRELATION_ID, APP_CORRELATION_ID);
        headers.add(Constants.HEADER_X_ORGANISATION_ID, ORGANISATION_ID);
        // headers.add(Constants.HEADER_X_ORIGINATING_SYSTEM_ID, ORIGINATING_SYSTEM_ID);
        headers.add(Constants.HEADER_X_CONSUMER_TYPE, CONSUMER_TYPE);
        return headers;
    }

    public static HttpHeaders createHeadersMissingXConsumerType() {
        HttpHeaders headers = new HttpHeaders();
        headers.add(Constants.HEADER_X_MESSAGE_ID, MESSAGE_ID);
        headers.add(Constants.HEADER_X_APPCORRELATION_ID, APP_CORRELATION_ID);
        headers.add(Constants.HEADER_X_ORGANISATION_ID, ORGANISATION_ID);
        headers.add(Constants.HEADER_X_ORIGINATING_SYSTEM_ID, ORIGINATING_SYSTEM_ID);
        // headers.add(Constants.HEADER_X_CONSUMER_TYPE, CONSUMER_TYPE);
        return headers;
    }

    public static HttpHeaders createInvalidHeaders() {
        HttpHeaders inValidHeaders = new HttpHeaders();
        inValidHeaders.add(Constants.HEADER_X_MESSAGE_ID, MESSAGE_ID);
        //inValidHeaders.add(Constants.HEADER_X_APPCORRELATION_ID, APP_CORRELATION_ID);
        inValidHeaders.add(Constants.HEADER_X_CONSUMER_TYPE, "User");
        inValidHeaders.add(Constants.HEADER_X_ORGANISATION_ID, ORGANISATION_ID);
        inValidHeaders.add(Constants.HEADER_X_ORIGINATING_SYSTEM_ID, ORIGINATING_SYSTEM_ID);
        return inValidHeaders;
    }
}
