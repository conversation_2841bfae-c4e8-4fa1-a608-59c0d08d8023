package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common;

import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.skyscreamer.jsonassert.JSONAssert;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;
import regression.junit.IntegrationTestSupport;

import java.io.IOException;
import java.net.URISyntaxException;

public class ControllerTestSupport {
  public static void callAndVerify(
      WebTestClient webClient,
      String uri,
      HttpMethod method,
      HttpHeaders headers,
      String requestBodyFile,
      String expectedResponseFile,
      HttpStatus expectedHttpStatus
  ) throws URISyntaxException, IOException, JSONException {
    String expectedResponse = StringUtils.isNotEmpty(expectedResponseFile)?IntegrationTestSupport.readFileAsString(expectedResponseFile):"";
    String requestBody = StringUtils.isNotEmpty(requestBodyFile)?IntegrationTestSupport.readFileAsString(requestBodyFile):"";

    String responseBody = webClient
        .method(method)
        .uri(uri)
        .headers(httpHeaders -> httpHeaders.addAll(headers))
        .contentType(MediaType.APPLICATION_JSON)
        .body(Mono.just(requestBody), String.class)
        .exchange()
        .expectStatus()
        .isEqualTo(expectedHttpStatus)
        .expectBody(String.class)
        .returnResult().getResponseBody();

    if(StringUtils.isEmpty(expectedResponseFile)) {
        return; // No response to verify
    }
    JSONAssert.assertEquals(expectedResponse, responseBody, true);
  }

}
