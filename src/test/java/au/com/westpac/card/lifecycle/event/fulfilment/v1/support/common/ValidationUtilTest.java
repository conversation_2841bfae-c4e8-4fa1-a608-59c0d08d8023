package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class ValidationUtilTest {

    @ParameterizedTest
    @ValueSource(strings = {"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"})
    void isValidEmailReturnsTrueForValidEmails(String email) {
        assertTrue(ValidationUtil.IS_VALID_EMAIL.test(email));
    }

    @ParameterizedTest
    @ValueSource(strings = {"plainaddress", "@missingusername.com", "<EMAIL>", "<EMAIL>"})
    void isValidEmailReturnsFalseForInvalidEmails(String email) {
        assertFalse(ValidationUtil.IS_VALID_EMAIL.test(email));
    }

    @Test
    void validateHeadersThrowsExceptionForInvalidHeaders() {
        HeaderRequestDTO invalidHeader = new HeaderRequestDTO();
        Set violations = Set.of(mock(ConstraintViolation.class));
        Validator validator = mock(Validator.class);
        when(validator.validate(invalidHeader)).thenReturn(violations);
        ValidationUtil validationUtil = new ValidationUtil(validator);

        ConstraintViolationException exception = assertThrows(
                ConstraintViolationException.class,
                () -> validationUtil.validateHeaders(invalidHeader)
        );

        assertEquals(Constants.API_HEADER_INVALID_EXCEPTION_MESSAGE, exception.getMessage());
        assertEquals(violations, exception.getConstraintViolations());
    }

    @Test
    void validateHeadersDoesNotThrowForValidHeaders() {
        HeaderRequestDTO validHeader = new HeaderRequestDTO();
        Validator validator = mock(Validator.class);
        when(validator.validate(validHeader)).thenReturn(Set.of());
        ValidationUtil validationUtil = new ValidationUtil(validator);
        assertDoesNotThrow(() -> validationUtil.validateHeaders(validHeader));
    }

}
