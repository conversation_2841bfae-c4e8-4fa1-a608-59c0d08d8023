package au.com.westpac.card.lifecycle.event.fulfilment.v1.biz.fulfilment;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.FulfilmentRequestBody;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.FulfilmentRequestCreationResponse;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.FulfilmentRequestResponse;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.CFRequest;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.repository.CFRequestRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import regression.junit.IntegrationTestSupport;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FulfilmentRequestServiceTest {

    @Mock
    private CFRequestRepository cfRequestRepository;

    @InjectMocks
    private FulfilmentRequestService fulfilmentRequestService;

    private FulfilmentRequestBody createRequest;
    private FulfilmentRequestCreationResponse expectedCreateResponse;
    private CFRequest cfRequestEntity;
    private FulfilmentRequestResponse expectedGetResponse;

    @BeforeEach
    void setUp() throws Exception {
        createRequest = IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-request.json",
                FulfilmentRequestBody.class
        );
        expectedCreateResponse = IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-response.json",
                FulfilmentRequestCreationResponse.class
        );
        cfRequestEntity = IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/CFRequest1.json",
                CFRequest.class
        );
        expectedGetResponse = IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-get-response.json",
                FulfilmentRequestResponse.class
        );
    }

    @Test
    void testCreate() {
        // Mock: Repository saves and returns entity with ID
        when(cfRequestRepository.save(any(CFRequest.class))).thenAnswer(invocation -> {
            CFRequest entity = invocation.getArgument(0);
            entity.setId(1234567L);
            return entity;
        });

        FulfilmentRequestCreationResponse response = fulfilmentRequestService.create(createRequest);

        assertThat(response).isNotNull();
        assertThat(response.getData()).isNotNull();
        assertThat(response.getData().getRequestId()).isNotEmpty();

        verify(cfRequestRepository).save(any(CFRequest.class));
    }

    @Test
    void testGetById() {
        // Mock: Repository returns entity
        when(cfRequestRepository.findById(1L)).thenReturn(Optional.of(cfRequestEntity));

        FulfilmentRequestResponse response = fulfilmentRequestService.getById(1L);

        assertThat(response).isNotNull();
        assertThat(response.getData()).isNotNull();
        assertThat(response.getData().getRequestId()).isEqualTo(cfRequestEntity.getId().toString());

        verify(cfRequestRepository).findById(1L);
    }

    @Test
    void testUpdate() {
        // Mock: Repository returns existing entity, then saves updated entity
        when(cfRequestRepository.findById(1L)).thenReturn(Optional.of(cfRequestEntity));
        when(cfRequestRepository.save(any(CFRequest.class))).thenAnswer(invocation -> invocation.getArgument(0));

        fulfilmentRequestService.update(1L, createRequest);

        verify(cfRequestRepository).findById(1L);
        verify(cfRequestRepository).save(any(CFRequest.class));
    }
}