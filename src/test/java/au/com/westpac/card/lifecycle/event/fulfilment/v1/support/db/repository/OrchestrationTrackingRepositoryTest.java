package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.repository;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.CFRequest;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.OrchestrationTracking;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import regression.junit.IntegrationTestSupport;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;

@DataJpaTest
class OrchestrationTrackingRepositoryTest {

    static {
        System.setProperty("gateway.host", "gw.dev1.api.srv.westpac.com.au");
    }

    @Autowired
    private OrchestrationTrackingRepository repository;

    @Autowired
    private CFRequestRepository requestRepository;

    @Autowired
    private jakarta.persistence.EntityManager entityManager;

    @Test
    @DisplayName("CRUD operations for OrchestrationTracking using IntegrationTestSupport and JSON test data")
    void testCrud_withJsonTestData() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new com.fasterxml.jackson.datatype.jsr310.JavaTimeModule());

        // Load test data from JSON files
        CFRequest request = IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/support/db/repository/cfrequest.json",
                CFRequest.class
        );
        OrchestrationTracking expected = IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/support/db/repository/orchestrationtracking.json",
                OrchestrationTracking.class
        );

        // Save parent and wire up relationship
        request = requestRepository.save(request);
        expected.setCfRequestId(request.getId());

        // Create
        OrchestrationTracking saved = repository.save(objectMapper.readValue(objectMapper.writeValueAsString(expected), OrchestrationTracking.class));
        assertThat(saved.getId()).isNotNull();

        // Read and assert using jsonAssert
        Optional<OrchestrationTracking> found = repository.findById(saved.getId());
        assertThat(found).isPresent();

        // Set dynamic fields for expected
        expected.setId(saved.getId());
        String expectedJson = objectMapper.writeValueAsString(expected);
        String actualJson = objectMapper.writeValueAsString(found.get());
        JSONAssert.assertEquals(expectedJson, actualJson, JSONCompareMode.STRICT);

        // Update
        saved.setStatus("COMPLETED");
        repository.save(saved);
        Optional<OrchestrationTracking> updated = repository.findById(saved.getId());
        assertThat(updated).isPresent();

        // Update expected object for comparison
        expected.setStatus("COMPLETED");
        expectedJson = objectMapper.writeValueAsString(expected);
        actualJson = objectMapper.writeValueAsString(updated.get());
        JSONAssert.assertEquals(expectedJson, actualJson, JSONCompareMode.STRICT);

        // Unit test: updateAllFieldsByWorkflowId
        java.time.OffsetDateTime newUpdatedDateTime = java.time.OffsetDateTime.parse("2023-01-02T14:00:00+10:00");
        java.math.BigDecimal newDuration = new java.math.BigDecimal("4321");
        int updatedRows = repository.updateAllFieldsByWorkflowId(
            "WF-12345",
            request.getId(),
            "UPDATED_STATUS",
            99L,
            newUpdatedDateTime,
            "NEW_PARENT_WF",
            "NewWorkflowName",
            "NewTask",
            "UpdatedReason",
            newDuration
        );
        // Assert the number of updated rows is exactly 1
        assertThat(updatedRows)
            .as("updateAllFieldsByWorkflowId should return 1 when a single row is updated, but was: " + updatedRows)
            .isEqualTo(1);

        // Clear persistence context to ensure fresh fetch from DB
        entityManager.clear();

        Optional<OrchestrationTracking> updatedByWorkflowId = repository.findByWorkflowId("WF-12345");
        assertThat(updatedByWorkflowId)
            .as("Should find updated OrchestrationTracking by workflowId")
            .isPresent();
        OrchestrationTracking updatedEntity = updatedByWorkflowId.get();
        assertThat(updatedEntity.getStatus()).isEqualTo("UPDATED_STATUS");
        assertThat(updatedEntity.getRetryCount()).isEqualTo(99L);
        assertThat(updatedEntity.getUpdatedDateTime()).isEqualTo(newUpdatedDateTime);
        assertThat(updatedEntity.getParentWorkflowId()).isEqualTo("NEW_PARENT_WF");
        assertThat(updatedEntity.getWorkflowName()).isEqualTo("NewWorkflowName");
        assertThat(updatedEntity.getCurrentTask()).isEqualTo("NewTask");
        assertThat(updatedEntity.getErrorReason()).isEqualTo("UpdatedReason");

        // Test: update non-existing record
        int nonExistingUpdate = repository.updateAllFieldsByWorkflowId(
            "NON_EXISTENT_WF",
            request.getId(),
            "SHOULD_NOT_UPDATE",
            0L,
            newUpdatedDateTime,
            "NO_PARENT",
            "NoWorkflow",
            "NoTask",
            "NoReason",
            newDuration
        );
        assertThat(nonExistingUpdate)
            .as("updateAllFieldsByWorkflowId should return 0 when no record matches the workflowId")
            .isEqualTo(0);

        // Delete
        repository.delete(saved);
        Optional<OrchestrationTracking> deleted = repository.findById(saved.getId());
        assertThat(deleted).isNotPresent();
    }
}