package au.com.westpac.card.lifecycle.event.fulfilment.v1.biz.orchestration;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.OrchestrationTrackingEvent;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.OrchestrationTrackingEventCreatedResponse;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.OrchestrationTracking;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.repository.OrchestrationTrackingRepository;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.skyscreamer.jsonassert.JSONAssert;
import regression.junit.IntegrationTestSupport;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OrchestrationTrackingServiceTest {

    @Mock
    private OrchestrationTrackingRepository repository;

    @InjectMocks
    private OrchestrationTrackingService service;

    @Test
    @DisplayName("create() should map request, save entity, and return response with workflowId (using JSON files and jsonAssert)")
    void testCreate_withJsonFilesAndJsonAssert() throws Exception {
        // Read request, entity, and expected response from JSON files
        OrchestrationTrackingEvent request = IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request.json",
                OrchestrationTrackingEvent.class
        );
        OrchestrationTracking savedEntity = IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-entity.json",
                OrchestrationTracking.class
        );
        String expectedResponseJson = IntegrationTestSupport.readFileAsString(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-response.json"
        );

        // Mock repository save
        when(repository.save(any(OrchestrationTracking.class))).thenReturn(savedEntity);

        // Measure execution time
        long start = System.nanoTime();
        OrchestrationTrackingEventCreatedResponse response = service.create(request);
        long end = System.nanoTime();
        long durationMs = (end - start) / 1_000_000;

        // Serialize actual response to JSON
        String actualResponseJson = IntegrationTestSupport.objectMapper.writeValueAsString(response);

        // Assert JSON equality (non-strict)
        JSONAssert.assertEquals(expectedResponseJson, actualResponseJson, false);

        // Assert execution time is reasonable (e.g., < 100ms)
        assert(durationMs < 100) : "Execution time too high: " + durationMs + "ms";
    }

    @Test
    @DisplayName("update() should save updated entity if id exists")
    void testUpdateSuccess() {
        String id = "uuid";
        OrchestrationTrackingEvent request = new OrchestrationTrackingEvent();

        when(repository.updateAllFieldsByWorkflowId(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(1);

        service.update(id, request);

        verify(repository).updateAllFieldsByWorkflowId(any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    @DisplayName("update() should throw EntityNotFoundException if id does not exist")
    void testUpdateNotFound() {
        String id = "uuid";
        OrchestrationTrackingEvent request = new OrchestrationTrackingEvent();

        when(repository.updateAllFieldsByWorkflowId(any(), any(), any(), any(), any(), any(), any(), any(), any(), any())).thenReturn(0);

        assertThatThrownBy(() -> service.update(id, request))
                .isInstanceOf(EntityNotFoundException.class)
                .hasMessageContaining("OrchestrationTracking not found for workflowId " + id);
    }
}