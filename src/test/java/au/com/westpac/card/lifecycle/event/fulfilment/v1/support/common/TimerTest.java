package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class TimerTest {

    @Test
    void toString_ShouldIncludeSourceAndDestination_WhenFineGrainIsFalse() {
        Timer timer = new Timer("ClientA", "ServiceB");
        String result = timer.toString();

        assertTrue(result.contains("FROM: ClientA"));
        assertTrue(result.contains("TO: ServiceB"));
        assertTrue(result.contains("Execution Time taken :: "));
        assertTrue(result.contains("@@@ "));
        assertTrue(result.contains("ms @@@"));
    }

    @Test
    void toString_ShouldNotIncludeSourceAndDestination_WhenFineGrainIsTrue() throws IllegalAccessException, NoSuchFieldException {
        Timer timer = new Timer("ClientA", "ServiceB");
        java.lang.reflect.Field fineGrainField = Timer.class.getDeclaredField("fineGrain");
        fineGrainField.setAccessible(true);
        fineGrainField.set(timer, true); // Accessing the private field using reflection
        String result = timer.toString();

        assertFalse(result.contains("FROM: ClientA"));
        assertFalse(result.contains("TO: ServiceB"));
        assertTrue(result.contains("Execution Time taken :: "));
        assertTrue(result.contains("@@@ "));
        assertTrue(result.contains("ms @@@"));
    }

    @Test
    void toString_ShouldIncludeElapsedTime() {
        Timer timer = new Timer("ClientA", "ServiceB");
        String result = timer.toString();

        assertTrue(result.matches(".*:ELAPSEDMS: \\d+.*"));
    }
}
