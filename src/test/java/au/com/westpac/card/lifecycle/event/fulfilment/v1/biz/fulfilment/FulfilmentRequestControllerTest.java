package au.com.westpac.card.lifecycle.event.fulfilment.v1.biz.fulfilment;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.*;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common.ControllerTestSupport;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.test.web.reactive.server.WebTestClient;
import regression.junit.IntegrationTestSupport;

import java.io.IOException;
import java.net.URISyntaxException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

@SpringBootTest()
@AutoConfigureMockMvc
public class FulfilmentRequestControllerTest {

    @MockBean
    private FulfilmentRequestService fulfilmentRequestService;

    static {
        System.setProperty("gateway.host", "gw.dev1.api.srv.westpac.com.au");
    }

    @Test
    void testCreateFulfilmentRequest(@Autowired WebTestClient webClient) throws URISyntaxException, IOException, JSONException {
        // Mock the service response using JSON file
        FulfilmentRequestCreationResponse mockResponse = regression.junit.IntegrationTestSupport.readObjectFromJson(
            "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-response.json",
            FulfilmentRequestCreationResponse.class
        );

        Mockito.when(fulfilmentRequestService.create(any(FulfilmentRequestBody.class)))
            .thenReturn(mockResponse);

        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createValidHeaders();

        ControllerTestSupport.callAndVerify(
            webClient,
            "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests?brandSilo=WPAC",
            HttpMethod.POST,
            headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-request.json",
            "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-response.json",
            HttpStatus.CREATED
        );
    }

    @Test
    void testCreateFulfilmentRequest_MissingXMessageId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXMessageId();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests?brandSilo=WPAC",
                HttpMethod.POST,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-message-id.json",
                HttpStatus.BAD_REQUEST
        );
    }

    @Test
    void testCreateFulfilmentRequest_MissingXAppCorrelationId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXAppCorrelationId();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests?brandSilo=WPAC",
                HttpMethod.POST,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-correlation-id.json",
                HttpStatus.BAD_REQUEST
        );
    }

    @Test
    void testCreateFulfilmentRequest_MissingXOrganisationId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXOrganisationId();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests?brandSilo=WPAC",
                HttpMethod.POST,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-organisation-id.json",
                HttpStatus.BAD_REQUEST
        );
    }

    @Test
    void testCreateFulfilmentRequest_MissingXOriginatingSystemId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXOriginatingSystemId();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests?brandSilo=WPAC",
                HttpMethod.POST,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-originating-system-id.json",
                HttpStatus.BAD_REQUEST
        );
    }

    @Test
    void testCreateFulfilmentRequest_MissingXConsumerType(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXConsumerType();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests?brandSilo=WPAC",
                HttpMethod.POST,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-consumer-type.json",
                HttpStatus.BAD_REQUEST
        );
    }

    @Test
    void testUpdateFulfilmentRequest(@Autowired WebTestClient webClient) throws URISyntaxException, IOException, JSONException {
        Mockito.doNothing().when(fulfilmentRequestService).update(eq(12345L), any(FulfilmentRequestBody.class));

        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createValidHeaders();

        ControllerTestSupport.callAndVerify(
            webClient,
            "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/12345?brandSilo=WPAC",
            HttpMethod.PUT,
            headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-request.json",
            null,
            HttpStatus.NO_CONTENT
        );
    }

    @Test
    void testUpdateFulfilmentRequest_BadRequestBody(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXAppCorrelationId();
        // Use a request body missing required fields (simulate bad request)
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/12345?brandSilo=WPAC",
                HttpMethod.PUT,
                headers,
                // Use an existing bad request body or create a minimal invalid JSON file if needed
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-request.json", // You may want to create a specific bad body JSON
                null,
                HttpStatus.BAD_REQUEST
        );
    }
    @Test
    void testUpdateFulfilmentRequest_MissingXMessageId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXMessageId();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/1?brandSilo=WPAC",
                HttpMethod.PUT,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-message-id.json",
                HttpStatus.BAD_REQUEST
        );
    }

    @Test
    void testUpdateFulfilmentRequest_MissingXAppCorrelationId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXAppCorrelationId();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/1?brandSilo=WPAC",
                HttpMethod.PUT,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-correlation-id.json",
                HttpStatus.BAD_REQUEST
        );
    }

    @Test
    void testUpdateFulfilmentRequest_MissingXOrganisationId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXOrganisationId();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/1?brandSilo=WPAC",
                HttpMethod.PUT,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-organisation-id.json",
                HttpStatus.BAD_REQUEST
        );
    }

    @Test
    void testUpdateFulfilmentRequest_MissingXOriginatingSystemId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXOriginatingSystemId();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/1?brandSilo=WPAC",
                HttpMethod.PUT,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-originating-system-id.json",
                HttpStatus.BAD_REQUEST
        );
    }

    @Test
    void testUpdateFulfilmentRequest_MissingXConsumerType(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXConsumerType();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/1?brandSilo=WPAC",
                HttpMethod.PUT,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-consumer-type.json",
                HttpStatus.BAD_REQUEST
        );
    }


    @Test
    void testUpdateFulfilmentRequest_MissingBrandSilo(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createValidHeaders();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/12345", // No brandSilo param
                HttpMethod.PUT,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-create-request.json",
                null,
                HttpStatus.BAD_REQUEST
        );
    }

    @Test
    void testGetFulfilmentRequestById(@Autowired WebTestClient webClient) throws Exception {
        // Arrange
        long requestId = 12345L;
        // Create a minimal mock response
        FulfilmentRequestResponse mockResponse = IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-get-response.json",
                FulfilmentRequestResponse.class
        );

        Mockito.when(fulfilmentRequestService.getById(requestId)).thenReturn(mockResponse);

        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createValidHeaders();

        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/" + requestId + "?brandSilo=WPAC",
                HttpMethod.GET,
                headers,
                null,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-get-response.json", // Optionally provide a JSON file to compare response
                HttpStatus.OK
        );
    }

    @Test
    void testSearchFulfilmentRequests(@Autowired WebTestClient webClient) throws Exception {

        FulfilmentRequestSearchResponse mockResponse = IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-search-response.json",
                FulfilmentRequestSearchResponse.class
        );
        Mockito.when(fulfilmentRequestService.search(any(FulfilmentRequestSearchRequest.class)))
                .thenReturn(mockResponse);

        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createValidHeaders();

        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequestSearch?brandSilo=WPAC",
                HttpMethod.POST,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-search-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/fulfilment-request-search-response.json", // Optionally provide a JSON file to compare response
                HttpStatus.OK
        );
    }

    @Test
    void testGetFulfilmentRequestById_TrackingEventNotFound(@Autowired WebTestClient webClient) throws URISyntaxException, IOException, JSONException {
        // Mock the service update method (void method, so we use doNothing)
        Mockito.doThrow(new FulfilmentNotFoundException("Test exception"))
                .when(fulfilmentRequestService)
                .getById(eq(42L));

        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createValidHeaders();

        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/42?brandSilo=WPAC",
                HttpMethod.GET,
                headers,
                null,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/fulfilment/not-found-response.json",
                HttpStatus.NOT_FOUND
        );
    }
}