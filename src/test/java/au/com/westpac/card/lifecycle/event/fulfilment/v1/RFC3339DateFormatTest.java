package au.com.westpac.card.lifecycle.event.fulfilment.v1;

import org.junit.jupiter.api.Test;

import java.text.FieldPosition;
import java.text.ParsePosition;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

public class RFC3339DateFormatTest {

  @Test
  void testParseValidDate() {
    RFC3339DateFormat dateFormat = new RFC3339DateFormat();
    String dateString = "2023-10-12T07:20:50.52Z";
    Date date = dateFormat.parse(dateString, new ParsePosition(0));
    assertNotNull(date);
  }

  @Test
  void testParseInvalidDate() {
    RFC3339DateFormat dateFormat = new RFC3339DateFormat();
    String dateString = "invalid-date";
    Date date = dateFormat.parse(dateString, new ParsePosition(0));
    assertNull(date);
  }

  @Test
  void testFormatDate() {
    RFC3339DateFormat dateFormat = new RFC3339DateFormat();
    Date date = new Date(0); // Epoch time
    String formattedDate = dateFormat.format(date, new StringBuffer(), new FieldPosition(0)).toString();
    assertEquals("1970-01-01T00:00:00.000+00:00", formattedDate);
  }

}