package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.repository;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@DataJpaTest
class CFRequestRepositorySearchTest {

    static {
        System.setProperty("gateway.host", "gw.dev1.api.srv.westpac.com.au");
    }

    @Autowired
    private CFRequestRepository cfRequestRepository;

    @Autowired
    private CFRequestFacilityDetailRepository facilityDetailRepository;

    @Autowired
    private CFRequestCustomerDetailRepository customerDetailRepository;

    @Autowired
    private CFRequestAddressDetailRepository addressDetailRepository;

    @Autowired
    private CFRequestCardDetailRepository cardDetailRepository;

    private CFRequest testRequest1;
    private CFRequest testRequest2;
    private CFRequest testRequest3;

    @BeforeEach
    void setUp() throws Exception {
        // Clean up any existing data
        cfRequestRepository.deleteAll();

        // Load test data from JSON files using IntegrationTestSupport
        testRequest1 = regression.junit.IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/support/db/repository/cfrequest_example1.json",
            au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.CFRequest.class
        );
        testRequest1.getFacilityDetail().setCfRequest(testRequest1);
        CFRequestCustomerDetail testCustomer1 = testRequest1.getFacilityDetail().getCustomerDetails().stream().findFirst().get();
        testCustomer1.setCfRequestFacilityDetail(testRequest1.getFacilityDetail());
        testCustomer1.getCardDetails().stream().findFirst().get().setCfRequestCustomerDetail(testCustomer1);
        testRequest1.getFacilityDetail().getAddressDetails().stream().findFirst().get().setCfRequestFacilityDetail(testRequest1.getFacilityDetail());
        testRequest2 = regression.junit.IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/support/db/repository/cfrequest_example2.json",
            au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.CFRequest.class
        );
        testRequest2.getFacilityDetail().setCfRequest(testRequest2);
        testRequest2.getFacilityDetail().getCustomerDetails().stream().findFirst().get().setCfRequestFacilityDetail(testRequest2.getFacilityDetail());
//        testRequest2.getFacilityDetail().getAddressDetails().stream().findFirst().get().setCfRequestFacilityDetail(testRequest2.getFacilityDetail());
        testRequest3 = regression.junit.IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/support/db/repository/cfrequest_example3.json",
            au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.CFRequest.class
        );
        testRequest3.getFacilityDetail().setCfRequest(testRequest3);
        testRequest3.getFacilityDetail().getAddressDetails().stream().findFirst().get().setCfRequestFacilityDetail(testRequest3.getFacilityDetail());
        CFRequestCustomerDetail testCustomer3 = testRequest3.getFacilityDetail().getCustomerDetails().stream().findFirst().get();
        testCustomer3.setCfRequestFacilityDetail(testRequest3.getFacilityDetail());
        testCustomer3.getCardDetails().stream().findFirst().get().setCfRequestCustomerDetail(testCustomer3);

        // Save the loaded test data to the repository
        testRequest1 = cfRequestRepository.save(testRequest1);
        testRequest2 = cfRequestRepository.save(testRequest2);
        testRequest3 = cfRequestRepository.save(testRequest3);
    }

    // Test data creation moved to JSON files and loaded in setUp()

    @Test
    void testFindAllByExample_WithType() {
        // Given: Example with type only
        CFRequest example = CFRequest.builder()
                .type("cardfacilityorigination")
                .build();

        ExampleMatcher matcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withIgnoreNullValues();

        Example<CFRequest> exampleQuery = Example.of(example, matcher);

        // When: Search using example
        List<CFRequest> results = cfRequestRepository.findAll(exampleQuery);

        // Then: Should find 2 requests with matching type
        assertThat(results).hasSize(2);
        assertThat(results).extracting(CFRequest::getType)
                .containsOnly("cardfacilityorigination");
        assertThat(results).extracting(CFRequest::getId)
                .containsExactlyInAnyOrder(testRequest1.getId(), testRequest3.getId());
    }

    @Test
    void testFindAllByExample_WithApplicationReferenceNumber() {
        // Given: Example with application reference number
        CFRequest example = CFRequest.builder()
                .applicationReferenceNumber("APP-REF-2024-001234")
                .build();

        ExampleMatcher matcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withIgnoreNullValues();

        Example<CFRequest> exampleQuery = Example.of(example, matcher);

        // When: Search using example
        List<CFRequest> results = cfRequestRepository.findAll(exampleQuery);

        // Then: Should find exactly 1 request
        assertThat(results).hasSize(1);
        assertThat(results.get(0).getApplicationReferenceNumber())
                .isEqualTo("APP-REF-2024-001234");
        assertThat(results.get(0).getId()).isEqualTo(testRequest1.getId());
    }

    @Test
    void testFindAllByExample_WithMultipleCriteria() {
        // Given: Example with multiple criteria
        CFRequest example = CFRequest.builder()
                .type("cardfacilityorigination")
                .sourceChannel("WL")
                .build();

        ExampleMatcher matcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withIgnoreNullValues();

        Example<CFRequest> exampleQuery = Example.of(example, matcher);

        // When: Search using example
        List<CFRequest> results = cfRequestRepository.findAll(exampleQuery);

        // Then: Should find 2 requests matching both criteria
        assertThat(results).hasSize(2);
        assertThat(results).extracting(CFRequest::getType)
                .containsOnly("cardfacilityorigination");
        assertThat(results).extracting(CFRequest::getSourceChannel)
                .containsOnly("WL");
    }

    @Test
    void testFindAllByExample_WithPagination() {
        // Given: Example with type and pagination
        CFRequest example = CFRequest.builder()
                .type("cardfacilityorigination")
                .build();

        ExampleMatcher matcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withIgnoreNullValues();

        Example<CFRequest> exampleQuery = Example.of(example, matcher);
        PageRequest pageRequest = PageRequest.of(0, 1);

        // When: Search using example with pagination
        Page<CFRequest> results = cfRequestRepository.findAll(exampleQuery, pageRequest);

        // Then: Should return paginated results
        assertThat(results.getContent()).hasSize(1);
        assertThat(results.getTotalElements()).isEqualTo(2);
        assertThat(results.getTotalPages()).isEqualTo(2);
        assertThat(results.getNumber()).isEqualTo(0);
        assertThat(results.getSize()).isEqualTo(1);
    }

    @Test
    void testFindAllByExample_NoMatches() {
        // Given: Example with non-existent criteria
        CFRequest example = CFRequest.builder()
                .type("nonexistenttype")
                .build();

        ExampleMatcher matcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withIgnoreNullValues();

        Example<CFRequest> exampleQuery = Example.of(example, matcher);

        // When: Search using example
        List<CFRequest> results = cfRequestRepository.findAll(exampleQuery);

        // Then: Should return empty list
        assertThat(results).isEmpty();
    }

    @Test
    void testFindAllByExample_CaseInsensitive() {
        // Given: Example with different case
        CFRequest example = CFRequest.builder()
                .type("CARDFACILITYORIGINATION") // Upper case
                .build();

        ExampleMatcher matcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withIgnoreNullValues();

        Example<CFRequest> exampleQuery = Example.of(example, matcher);

        // When: Search using example
        List<CFRequest> results = cfRequestRepository.findAll(exampleQuery);

        // Then: Should find matches despite case difference
        assertThat(results).hasSize(2);
        assertThat(results).extracting(CFRequest::getType)
                .containsOnly("cardfacilityorigination");
    }

    @Test
    void testFindAllByExample_WithExactStringMatching() {
        // Given: Example with exact string matching
        CFRequest example = CFRequest.builder()
                .applicationReferenceNumber("APP-REF-2024-001234")
                .build();

        ExampleMatcher matcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withIgnoreNullValues()
                .withStringMatcher(ExampleMatcher.StringMatcher.EXACT);

        Example<CFRequest> exampleQuery = Example.of(example, matcher);

        // When: Search using example
        List<CFRequest> results = cfRequestRepository.findAll(exampleQuery);

        // Then: Should find exact match
        assertThat(results).hasSize(1);
        assertThat(results.get(0).getApplicationReferenceNumber())
                .isEqualTo("APP-REF-2024-001234");
    }

    @Test
    void testFindAllByExample_IgnoreAuditFields() {
        // Given: Example with audit fields that should be ignored
        CFRequest example = CFRequest.builder()
                .type("cardfacilityorigination")
                .createdBy("DIFFERENT_USER") // This should be ignored
                .updatedBy("ANOTHER_USER")   // This should be ignored
                .build();

        ExampleMatcher matcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withIgnoreNullValues()
                .withIgnorePaths("id", "createdDate", "updatedDate", "createdBy", "updatedBy");

        Example<CFRequest> exampleQuery = Example.of(example, matcher);

        // When: Search using example
        List<CFRequest> results = cfRequestRepository.findAll(exampleQuery);

        // Then: Should find matches ignoring audit fields
        assertThat(results).hasSize(2);
        assertThat(results).extracting(CFRequest::getType)
                .containsOnly("cardfacilityorigination");
    }

    @Test
    void testFindAllByExample_WithCustomerCriteria() {
        // Given: Search for requests by customer ID using customer repository
        // Note: JPA Example on CFRequest cannot search nested customer details directly
        // We need to search via the customer repository first, then get the related requests

        CFRequestCustomerDetail customerExample = CFRequestCustomerDetail.builder()
                .customerId("CUST123456789")
                .build();

        ExampleMatcher matcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withIgnoreNullValues()
                .withIgnorePaths("id");

        Example<CFRequestCustomerDetail> customerExampleQuery = Example.of(customerExample, matcher);

        // When: Search for customers first, then get their requests
        List<CFRequestCustomerDetail> customers = customerDetailRepository.findAll(customerExampleQuery);

        // Then: Should find exactly 1 customer with that ID
        assertThat(customers).hasSize(1);
        assertThat(customers.get(0).getCustomerId()).isEqualTo("CUST123456789");

        // And: Get the related CFRequest through the relationship
        CFRequest relatedRequest = customers.get(0).getCfRequestFacilityDetail().getCfRequest();
        assertThat(relatedRequest).isNotNull();
        assertThat(relatedRequest.getId()).isEqualTo(testRequest1.getId());
        assertThat(relatedRequest.getApplicationReferenceNumber()).isEqualTo("APP-REF-2024-001234");
    }

    @Test
    void testFindAllByExample_WithBankBrandCriteria() {
        // Given: Search for customers by bank brand
        CFRequestCustomerDetail customerExample = CFRequestCustomerDetail.builder()
                .bankBrand("WBC")
                .build();

        ExampleMatcher matcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withIgnoreNullValues()
                .withIgnorePaths("id");

        Example<CFRequestCustomerDetail> customerExampleQuery = Example.of(customerExample, matcher);

        // When: Search for customers by brand
        List<CFRequestCustomerDetail> customers = customerDetailRepository.findAll(customerExampleQuery);

        // Then: Should find exactly 1 customer with WBC brand
        assertThat(customers).hasSize(2);
        assertThat(customers.get(0).getBankBrand()).isEqualTo("WBC");
        assertThat(customers.get(0).getCustomerId()).isEqualTo("CUST123456789");

        // And: Verify the related request
        CFRequest relatedRequest = customers.get(0).getCfRequestFacilityDetail().getCfRequest();
        assertThat(relatedRequest.getType()).isEqualTo("cardfacilityorigination");
    }

    @Test
    void testFindAllByExample_WithSTGBrandCriteria() {
        // Given: Search for customers by STG bank brand
        CFRequestCustomerDetail customerExample = CFRequestCustomerDetail.builder()
                .bankBrand("STG")
                .build();

        ExampleMatcher matcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withIgnoreNullValues()
                .withIgnorePaths("id");

        Example<CFRequestCustomerDetail> customerExampleQuery = Example.of(customerExample, matcher);

        // When: Search for customers by STG brand
        List<CFRequestCustomerDetail> customers = customerDetailRepository.findAll(customerExampleQuery);

        // Then: Should find exactly 1 customer with STG brand
        assertThat(customers).hasSize(1);
        assertThat(customers.get(0).getBankBrand()).isEqualTo("STG");
        assertThat(customers.get(0).getCustomerId()).isEqualTo("CUST987654321");

        // And: Verify the related request
        CFRequest relatedRequest = customers.get(0).getCfRequestFacilityDetail().getCfRequest();
        assertThat(relatedRequest.getType()).isEqualTo("cardactivation");
    }

    @Test
    void testFindAllByExample_WithCustomerIdAndApplicationReferenceNumber() {
        // Given: Search combining customer criteria and request criteria
        // Step 1: Search for customers by customerId
        CFRequestCustomerDetail customerExample = CFRequestCustomerDetail.builder()
                .customerId("CUST123456789")
                .build();

        ExampleMatcher customerMatcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withIgnoreNullValues()
                .withIgnorePaths("id");

        Example<CFRequestCustomerDetail> customerExampleQuery = Example.of(customerExample, customerMatcher);

        // Step 2: Search for requests by applicationReferenceNumber
        CFRequest requestExample = CFRequest.builder()
                .applicationReferenceNumber("APP-REF-2024-001234")
                .build();

        ExampleMatcher requestMatcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withIgnoreNullValues()
                .withIgnorePaths("id");

        Example<CFRequest> requestExampleQuery = Example.of(requestExample, requestMatcher);

        // When: Execute both searches
        List<CFRequestCustomerDetail> customers = customerDetailRepository.findAll(customerExampleQuery);
        List<CFRequest> requests = cfRequestRepository.findAll(requestExampleQuery);

        // Then: Should find exactly 1 customer and 1 request
        assertThat(customers).hasSize(1);
        assertThat(requests).hasSize(1);

        // And: Verify they are related to the same CFRequest
        CFRequest customerRelatedRequest = customers.get(0).getCfRequestFacilityDetail().getCfRequest();
        CFRequest directRequest = requests.get(0);

        assertThat(customerRelatedRequest.getId()).isEqualTo(directRequest.getId());
        assertThat(customerRelatedRequest.getId()).isEqualTo(testRequest1.getId());

        // And: Verify all the expected values
        assertThat(customers.get(0).getCustomerId()).isEqualTo("CUST123456789");
        assertThat(directRequest.getApplicationReferenceNumber()).isEqualTo("APP-REF-2024-001234");
        assertThat(directRequest.getType()).isEqualTo("cardfacilityorigination");
    }

    @Test
    void testFindAllByExample_WithCustomerIdAndApplicationReferenceNumber_NoMatch() {
        // Given: Search with customer that exists but application reference that doesn't match
        CFRequestCustomerDetail customerExample = CFRequestCustomerDetail.builder()
                .customerId("CUST123456789") // This exists
                .build();

        CFRequest requestExample = CFRequest.builder()
                .applicationReferenceNumber("APP-REF-NONEXISTENT") // This doesn't exist
                .build();

        ExampleMatcher matcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withIgnoreNullValues()
                .withIgnorePaths("id");

        Example<CFRequestCustomerDetail> customerQuery = Example.of(customerExample, matcher);
        Example<CFRequest> requestQuery = Example.of(requestExample, matcher);

        // When: Execute both searches
        List<CFRequestCustomerDetail> customers = customerDetailRepository.findAll(customerQuery);
        List<CFRequest> requests = cfRequestRepository.findAll(requestQuery);

        // Then: Customer should be found but request should not
        assertThat(customers).hasSize(1);
        assertThat(requests).hasSize(0); // No matching application reference

        // And: This demonstrates that both criteria must match for a valid result
        assertThat(customers.get(0).getCustomerId()).isEqualTo("CUST123456789");
    }

    // Tests for the new custom query methods
    @Test
    void testFindByMultipleCriteria_AllFields() {
        // Given: Search using all available criteria
        String type = "cardfacilityorigination";
        String applicationReferenceNumber = "APP-REF-2024-001234";
        String bankBrand = "WBC";
        String customerId = "CUST123456789";

        // When: Search using custom query method
        List<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                type, applicationReferenceNumber, bankBrand, customerId);

        // Then: Should find exactly 1 matching request
        assertThat(results).hasSize(1);
        assertThat(results.get(0).getId()).isEqualTo(testRequest1.getId());
        assertThat(results.get(0).getType()).isEqualTo(type);
        assertThat(results.get(0).getApplicationReferenceNumber()).isEqualTo(applicationReferenceNumber);

        // And: Verify customer details through relationships
        CFRequestCustomerDetail customer = results.get(0).getFacilityDetail().getCustomerDetails().iterator().next();
        assertThat(customer.getBankBrand()).isEqualTo(bankBrand);
        assertThat(customer.getCustomerId()).isEqualTo(customerId);
    }

    @Test
    void testFindByMultipleCriteria_TypeAndBankBrand() {
        // Given: Search by type and bank brand only
        String type = "cardfacilityorigination";
        String bankBrand = "WBC";

        // When: Search using custom query method
        List<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                type, null, bankBrand, null);

        // Then: Should find exactly 1 matching request
        assertThat(results).hasSize(2);
        assertThat(results.get(0).getType()).isEqualTo(type);

        CFRequestCustomerDetail customer = results.get(0).getFacilityDetail().getCustomerDetails().iterator().next();
        assertThat(customer.getBankBrand()).isEqualTo(bankBrand);
    }

    @Test
    void testFindByMultipleCriteria_BankBrandOnly() {
        // Given: Search by bank brand only
        String bankBrand = "STG";

        // When: Search using custom query method
        List<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                null, null, bankBrand, null);

        // Then: Should find exactly 1 matching request
        assertThat(results).hasSize(1);
        assertThat(results.get(0).getId()).isEqualTo(testRequest2.getId());

        CFRequestCustomerDetail customer = results.get(0).getFacilityDetail().getCustomerDetails().iterator().next();
        assertThat(customer.getBankBrand()).isEqualTo(bankBrand);
    }

    @Test
    void testFindByMultipleCriteria_CustomerIdOnly() {
        // Given: Search by customer ID only
        String customerId = "CUST987654321";

        // When: Search using custom query method
        List<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                null, null, null, customerId);

        // Then: Should find exactly 1 matching request
        assertThat(results).hasSize(1);
        assertThat(results.get(0).getId()).isEqualTo(testRequest2.getId());

        CFRequestCustomerDetail customer = results.get(0).getFacilityDetail().getCustomerDetails().iterator().next();
        assertThat(customer.getCustomerId()).isEqualTo(customerId);
    }

    @Test
    void testFindByMultipleCriteria_NoMatches() {
        // Given: Search with criteria that don't match any records
        String type = "nonexistenttype";
        String bankBrand = "NONEXISTENT";

        // When: Search using custom query method
        List<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                type, null, bankBrand, null);

        // Then: Should return empty list
        assertThat(results).isEmpty();
    }

    @Test
    void testFindByMultipleCriteria_WithPagination() {
        // Given: Search with pagination
        String type = "cardfacilityorigination";
        PageRequest pageRequest = PageRequest.of(0, 1);

        // When: Search using custom query method with pagination
        Page<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                type, null, null, null, pageRequest);

        // Then: Should return paginated results
        assertThat(results.getContent()).hasSize(1);
        assertThat(results.getTotalElements()).isEqualTo(2); // 2 total cardfacilityorigination requests
        assertThat(results.getTotalPages()).isEqualTo(2);
        assertThat(results.getNumber()).isEqualTo(0);
        assertThat(results.getSize()).isEqualTo(1);
    }

    @Test
    void testFindByMultipleCriteria_ConflictingCriteria() {
        // Given: Search with conflicting criteria (type from request1, customer from request2)
        String type = "cardfacilityorigination"; // From testRequest1
        String customerId = "CUST987654321";      // From testRequest2

        // When: Search using custom query method
        List<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                type, null, null, customerId);

        // Then: Should return empty list (no request matches both criteria)
        assertThat(results).isEmpty();
    }

    @Test
    void testFindByMultipleCriteria_TypeAndApplicationRef() {
        // Given: Search by type and application reference number
        String type = "cardfacilityorigination";
        String applicationReferenceNumber = "APP-REF-2024-001234";

        // When: Search using custom query method
        List<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                type, applicationReferenceNumber, null, null);

        // Then: Should find exactly 1 matching request
        assertThat(results).hasSize(1);
        assertThat(results.get(0).getId()).isEqualTo(testRequest1.getId());
        assertThat(results.get(0).getType()).isEqualTo(type);
        assertThat(results.get(0).getApplicationReferenceNumber()).isEqualTo(applicationReferenceNumber);
    }

    @Test
    void testFindByMultipleCriteria_ApplicationRefAndBankBrand() {
        // Given: Search by application reference and bank brand
        String applicationReferenceNumber = "APP-REF-2024-005678";
        String bankBrand = "STG";

        // When: Search using custom query method
        List<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                null, applicationReferenceNumber, bankBrand, null);

        // Then: Should find exactly 1 matching request
        assertThat(results).hasSize(1);
        assertThat(results.get(0).getId()).isEqualTo(testRequest2.getId());
        assertThat(results.get(0).getApplicationReferenceNumber()).isEqualTo(applicationReferenceNumber);

        CFRequestCustomerDetail customer = results.get(0).getFacilityDetail().getCustomerDetails().iterator().next();
        assertThat(customer.getBankBrand()).isEqualTo(bankBrand);
    }

    @Test
    void testFindByMultipleCriteria_CustomerIdAndBankBrand() {
        // Given: Search by customer ID and bank brand (should match)
        String customerId = "CUST123456789";
        String bankBrand = "WBC";

        // When: Search using custom query method
        List<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                null, null, bankBrand, customerId);

        // Then: Should find exactly 1 matching request
        assertThat(results).hasSize(1);
        assertThat(results.get(0).getId()).isEqualTo(testRequest1.getId());

        CFRequestCustomerDetail customer = results.get(0).getFacilityDetail().getCustomerDetails().iterator().next();
        assertThat(customer.getCustomerId()).isEqualTo(customerId);
        assertThat(customer.getBankBrand()).isEqualTo(bankBrand);
    }

    @Test
    void testFindByMultipleCriteria_CustomerIdAndBankBrand_Mismatch() {
        // Given: Search by customer ID from one request and bank brand from another
        String customerId = "CUST123456789"; // From testRequest1 (WBC)
        String bankBrand = "STG";            // From testRequest2

        // When: Search using custom query method
        List<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                null, null, bankBrand, customerId);

        // Then: Should return empty list (no customer has both criteria)
        assertThat(results).isEmpty();
    }

    @Test
    void testFindByMultipleCriteria_AllNullParameters() {
        // Given: Search with all null parameters
        // When: Search using custom query method
        List<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                null, null, null, null);

        // Then: Should return all requests (no filtering)
        assertThat(results).hasSize(3); // testRequest1, testRequest2, testRequest3
        assertThat(results).extracting(CFRequest::getId)
                .containsExactlyInAnyOrder(testRequest1.getId(), testRequest2.getId(), testRequest3.getId());
    }

    @Test
    void testFindByMultipleCriteria_CaseInsensitiveType() {
        // Given: Search with different case for type
        String type = "CARDFACILITYORIGINATION"; // Upper case

        // When: Search using custom query method
        List<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                type, null, null, null);

        // Then: Should return empty list (exact match required, not case-insensitive)
        assertThat(results).isEmpty();
    }

    @Test
    void testFindByMultipleCriteria_WithPagination_FirstPage() {
        // Given: Search with pagination - first page
        String type = "cardfacilityorigination";
        PageRequest pageRequest = PageRequest.of(0, 1); // First page, 1 item

        // When: Search using custom query method with pagination
        Page<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                type, null, null, null, pageRequest);

        // Then: Should return first page with 1 item
        assertThat(results.getContent()).hasSize(1);
        assertThat(results.getTotalElements()).isEqualTo(2); // Total cardfacilityorigination requests
        assertThat(results.getTotalPages()).isEqualTo(2);
        assertThat(results.getNumber()).isEqualTo(0); // Current page number
        assertThat(results.getSize()).isEqualTo(1);   // Page size
        assertThat(results.isFirst()).isTrue();
        assertThat(results.isLast()).isFalse();
    }

    @Test
    void testFindByMultipleCriteria_WithPagination_SecondPage() {
        // Given: Search with pagination - second page
        String type = "cardfacilityorigination";
        PageRequest pageRequest = PageRequest.of(1, 1); // Second page, 1 item

        // When: Search using custom query method with pagination
        Page<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                type, null, null, null, pageRequest);

        // Then: Should return second page with 1 item
        assertThat(results.getContent()).hasSize(1);
        assertThat(results.getTotalElements()).isEqualTo(2);
        assertThat(results.getTotalPages()).isEqualTo(2);
        assertThat(results.getNumber()).isEqualTo(1); // Current page number
        assertThat(results.getSize()).isEqualTo(1);   // Page size
        assertThat(results.isFirst()).isFalse();
        assertThat(results.isLast()).isTrue();
    }

    @Test
    void testFindByMultipleCriteria_WithPagination_LargePageSize() {
        // Given: Search with pagination - large page size
        String bankBrand = "WBC";
        PageRequest pageRequest = PageRequest.of(0, 10); // Large page size

        // When: Search using custom query method with pagination
        Page<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                null, null, bankBrand, null, pageRequest);

        // Then: Should return all matching items in one page
        assertThat(results.getContent()).hasSize(2); // Only 1 WBC request
        assertThat(results.getTotalElements()).isEqualTo(2);
        assertThat(results.getTotalPages()).isEqualTo(1);
        assertThat(results.getNumber()).isEqualTo(0);
        assertThat(results.getSize()).isEqualTo(10);
        assertThat(results.isFirst()).isTrue();
        assertThat(results.isLast()).isTrue();
    }

    @Test
    void testFindByMultipleCriteria_WithPagination_EmptyResults() {
        // Given: Search with pagination for non-existent data
        String type = "nonexistenttype";
        PageRequest pageRequest = PageRequest.of(0, 5);

        // When: Search using custom query method with pagination
        Page<CFRequest> results = cfRequestRepository.findByMultipleCriteria(
                type, null, null, null, pageRequest);

        // Then: Should return empty page
        assertThat(results.getContent()).isEmpty();
        assertThat(results.getTotalElements()).isEqualTo(0);
        assertThat(results.getTotalPages()).isEqualTo(0);
        assertThat(results.getNumber()).isEqualTo(0);
        assertThat(results.getSize()).isEqualTo(5);
        assertThat(results.isFirst()).isTrue();
        assertThat(results.isLast()).isTrue();
    }

}
