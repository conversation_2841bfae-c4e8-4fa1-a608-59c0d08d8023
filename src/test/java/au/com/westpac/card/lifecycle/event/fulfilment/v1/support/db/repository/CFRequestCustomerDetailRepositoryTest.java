package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.repository;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.CFRequest;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.CFRequestCustomerDetail;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.CFRequestFacilityDetail;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import regression.junit.IntegrationTestSupport;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;

@DataJpaTest
class CFRequestCustomerDetailRepositoryTest {

    static {
        System.setProperty("gateway.host", "gw.dev1.api.srv.westpac.com.au");
    }

    @Autowired
    private CFRequestCustomerDetailRepository repository;

    @Autowired
    private CFRequestFacilityDetailRepository facilityDetailRepository;

    @Autowired
    private CFRequestRepository requestRepository;

    @Test
    @DisplayName("CRUD operations for CFRequestCustomerDetail using IntegrationTestSupport and JSON test data")
    void testCrud_withJsonTestData() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new com.fasterxml.jackson.datatype.jsr310.JavaTimeModule());

        // Load test data from JSON files
        CFRequest request = IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/support/db/repository/cfrequest.json",
                CFRequest.class
        );
        CFRequestFacilityDetail facilityDetail = IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/support/db/repository/cfrequestfacilitydetail.json",
                CFRequestFacilityDetail.class
        );
        CFRequestCustomerDetail expected = IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/support/db/repository/cfrequestcustomerdetail.json",
                CFRequestCustomerDetail.class
        );

        // Wire up relationships
        facilityDetail.setCfRequest(request);
        expected.setCfRequestFacilityDetail(facilityDetail);

        // Save entities in order
        request = requestRepository.save(request);
        facilityDetail = facilityDetailRepository.save(facilityDetail);

        // Create
        CFRequestCustomerDetail saved = repository.save(expected);
        assertThat(saved.getId()).isNotNull();

        // Read and assert using jsonAssert
        Optional<CFRequestCustomerDetail> found = repository.findById(saved.getId());
        assertThat(found).isPresent();

        // Set dynamic fields for expected
        expected.setId(saved.getId());
        String expectedJson = objectMapper.writeValueAsString(expected);
        String actualJson = objectMapper.writeValueAsString(found.get());
        JSONAssert.assertEquals(expectedJson, actualJson, JSONCompareMode.STRICT);

        // Update
        saved.setFirstName("Jane");
        repository.save(saved);
        Optional<CFRequestCustomerDetail> updated = repository.findById(saved.getId());
        assertThat(updated).isPresent();

        // Update expected object for comparison
        expected.setFirstName("Jane");
        expectedJson = objectMapper.writeValueAsString(expected);
        actualJson = objectMapper.writeValueAsString(updated.get());
        JSONAssert.assertEquals(expectedJson, actualJson, JSONCompareMode.STRICT);

        // Delete
        repository.delete(saved);
        Optional<CFRequestCustomerDetail> deleted = repository.findById(saved.getId());
        assertThat(deleted).isNotPresent();
    }
}