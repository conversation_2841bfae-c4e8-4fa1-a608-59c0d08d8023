package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;

class ApiUtilsTest {

    @Test
    void buildHeaderRequestDTO_ShouldBuildDTO_WhenAllHeadersArePresent() {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(Constants.HEADER_X_MESSAGE_ID, MockData.MESSAGE_ID);
        headerMap.put(Constants.HEADER_X_APPCORRELATION_ID, MockData.APP_CORRELATION_ID);
        headerMap.put(Constants.HEADER_X_ORGANISATION_ID, MockData.ORGANISATION_ID);
        headerMap.put(Constants.HEADER_X_ORIGINATING_SYSTEM_ID, MockData.ORIGINATING_SYSTEM_ID);
        headerMap.put(Constants.HEADER_X_CONSUMER_TYPE, MockData.CONSUMER_TYPE);

        HeaderRequestDTO result = ApiUtils.buildHeaderRequestDTO(headerMap);

        assertEquals(MockData.MESSAGE_ID, result.getXMessageId());
        assertEquals(MockData.APP_CORRELATION_ID, result.getXAppCorrelationId());
        assertEquals(MockData.ORGANISATION_ID, result.getXOrganisationId());
        assertEquals(MockData.ORIGINATING_SYSTEM_ID, result.getXOriginatingSystemId());
        assertEquals(MockData.CONSUMER_TYPE, result.getXConsumerType());
    }

    @Test
    void buildHeaderRequestDTO_ShouldHandleMissingHeaders() {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(Constants.HEADER_X_MESSAGE_ID, MockData.MESSAGE_ID);

        HeaderRequestDTO result = ApiUtils.buildHeaderRequestDTO(headerMap);

        assertEquals(MockData.MESSAGE_ID, result.getXMessageId());
        assertNull(result.getXAppCorrelationId());
        assertNull(result.getXOrganisationId());
        assertNull(result.getXOriginatingSystemId());
        assertNull(result.getXConsumerType());
    }

    @Test
    void buildHeaderRequestDTO_ShouldHandleEmptyHeaderMap() {
        Map<String, String> headerMap = new HashMap<>();

        HeaderRequestDTO result = ApiUtils.buildHeaderRequestDTO(headerMap);

        assertNull(result.getXMessageId());
        assertNull(result.getXAppCorrelationId());
        assertNull(result.getXOrganisationId());
        assertNull(result.getXOriginatingSystemId());
        assertNull(result.getXConsumerType());
    }

}