package au.com.westpac.card.lifecycle.event.fulfilment.v1.biz.orchestration;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.OrchestrationTrackingEvent;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.OrchestrationTrackingEventCreatedResponse;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.api.model.OrchestrationTrackingEventCreatedResponseData;
import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common.ControllerTestSupport;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.test.web.reactive.server.WebTestClient;

import java.io.IOException;
import java.net.URISyntaxException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

@SpringBootTest()
@AutoConfigureMockMvc
public class OrchestrationTrackingControllerTest {

    @MockBean
    private OrchestrationTrackingService orchestrationTrackingService;

    static {
        System.setProperty("gateway.host", "gw.dev1.api.srv.westpac.com.au");
    }

    @Test
    void testCreateOrchestrationTrackingEvent(@Autowired WebTestClient webClient) throws URISyntaxException, IOException, JSONException {
        // Mock the service response
        OrchestrationTrackingEventCreatedResponse mockResponse = OrchestrationTrackingEventCreatedResponse.builder()
            .data(OrchestrationTrackingEventCreatedResponseData.builder()
                .workflowId("42")
                .build())
            .build();

        Mockito.when(orchestrationTrackingService.create(any(OrchestrationTrackingEvent.class)))
            .thenReturn(mockResponse);

        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createValidHeaders();

        ControllerTestSupport.callAndVerify(
            webClient,
            "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents?brandSilo=WPAC",
            HttpMethod.POST,
            headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-response.json",
            HttpStatus.CREATED);
    }

    @Test
    void testUpdateOrchestrationTrackingEvent(@Autowired WebTestClient webClient) throws URISyntaxException, IOException, JSONException {
        // Mock the service update method (void method, so we use doNothing)
        Mockito.doNothing().when(orchestrationTrackingService).update(eq("42"), any(OrchestrationTrackingEvent.class));

        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createValidHeaders();

        ControllerTestSupport.callAndVerify(
            webClient,
            "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/42?brandSilo=WPAC",
            HttpMethod.PUT,
            headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request.json",
            null, // Update endpoint returns no content
            HttpStatus.NO_CONTENT);
    }

    @Test
    void testCreateOrchestrationTrackingEvent_MissingXMessageId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXMessageId();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents?brandSilo=WPAC",
                HttpMethod.POST,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-message-id.json",
                HttpStatus.BAD_REQUEST);
    }

    @Test
    void testCreateOrchestrationTrackingEvent_MissingXAppCorrelationId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXAppCorrelationId();
        ControllerTestSupport.callAndVerify(
            webClient,
            "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents?brandSilo=WPAC",
            HttpMethod.POST,
            headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request.json",
            "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-correlation-id.json",
            HttpStatus.BAD_REQUEST);
    }

    @Test
    void testCreateOrchestrationTrackingEvent_MissingXOrganisationId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXOrganisationId();
        ControllerTestSupport.callAndVerify(
            webClient,
            "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents?brandSilo=WPAC",
            HttpMethod.POST,
            headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request.json",
            "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-organisation-id.json",
            HttpStatus.BAD_REQUEST);
    }

    @Test
    void testCreateOrchestrationTrackingEvent_MissingXOriginatingSystemId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXOriginatingSystemId();
        ControllerTestSupport.callAndVerify(
            webClient,
            "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents?brandSilo=WPAC",
            HttpMethod.POST,
            headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request.json",
            "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-originating-system-id.json",
            HttpStatus.BAD_REQUEST);
    }

    @Test
    void testCreateOrchestrationTrackingEvent_MissingXConsumerType(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXConsumerType();
        ControllerTestSupport.callAndVerify(
            webClient,
            "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents?brandSilo=WPAC",
            HttpMethod.POST,
            headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request.json",
            "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-consumer-type.json",
            HttpStatus.BAD_REQUEST);
    }
    @Test
    void testUpdateOrchestrationTrackingEvent_MissingXMessageId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXMessageId();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/42?brandSilo=WPAC",
                HttpMethod.PUT,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-message-id.json",
                HttpStatus.BAD_REQUEST);
    }

    @Test
    void testUpdateOrchestrationTrackingEvent_MissingXAppCorrelationId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXAppCorrelationId();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/42?brandSilo=WPAC",
                HttpMethod.PUT,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-correlation-id.json",
                HttpStatus.BAD_REQUEST);
    }

    @Test
    void testUpdateOrchestrationTrackingEvent_MissingXOrganisationId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXOrganisationId();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/42?brandSilo=WPAC",
                HttpMethod.PUT,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-organisation-id.json",
                HttpStatus.BAD_REQUEST);
    }

    @Test
    void testUpdateOrchestrationTrackingEvent_MissingXOriginatingSystemId(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXOriginatingSystemId();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/42?brandSilo=WPAC",
                HttpMethod.PUT,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-originating-system-id.json",
                HttpStatus.BAD_REQUEST);
    }

    @Test
    void testUpdateOrchestrationTrackingEvent_MissingXConsumerType(@Autowired WebTestClient webClient) throws Exception {
        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createHeadersMissingXConsumerType();
        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/42?brandSilo=WPAC",
                HttpMethod.PUT,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-consumer-type.json",
                HttpStatus.BAD_REQUEST);
    }

    @Test
    void testUpdateOrchestrationTrackingEventMethodArgumentTypeMismatchException(@Autowired WebTestClient webClient) throws URISyntaxException, IOException, JSONException {
        // Mock the service update method (void method, so we use doNothing)
        Mockito.doNothing().when(orchestrationTrackingService).update(eq("42"), any(OrchestrationTrackingEvent.class));

        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createValidHeaders();

        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/42",
                HttpMethod.PUT,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/bad-request-missing-brandsilo.json",
                null, // Update endpoint returns no content
                HttpStatus.BAD_REQUEST);
    }

    @Test
    void testUpdateOrchestrationTrackingEventGeneralException(@Autowired WebTestClient webClient) throws URISyntaxException, IOException, JSONException {
        // Mock the service update method (void method, so we use doNothing)
        Mockito.doThrow(new RuntimeException("Test exception"))
                .when(orchestrationTrackingService)
                .update(eq("42"), any(OrchestrationTrackingEvent.class));

        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createValidHeaders();

        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/42?brandSilo=WPAC",
                HttpMethod.PUT,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/common/500.json", // Update endpoint returns no content
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Test
    void testUpdateOrchestrationTrackingEventBadBodyException(@Autowired WebTestClient webClient) throws URISyntaxException, IOException, JSONException {
        // Mock the service update method (void method, so we use doNothing)

        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createValidHeaders();

        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/42?brandSilo=WPAC",
                HttpMethod.PUT,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request-bad-body.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/bad-body.json", // Update endpoint returns no content
                HttpStatus.BAD_REQUEST);
    }

    @Test
    void testUpdateOrchestrationTrackingEventNotFound(@Autowired WebTestClient webClient) throws URISyntaxException, IOException, JSONException {
        // Mock the service update method (void method, so we use doNothing)
        Mockito.doThrow(new OrchestrationNotFoundException("Test exception"))
                .when(orchestrationTrackingService)
                .update(eq("42"), any(OrchestrationTrackingEvent.class));

        HttpHeaders headers = au.com.westpac.card.lifecycle.event.fulfilment.v1.MockData.createValidHeaders();

        ControllerTestSupport.callAndVerify(
                webClient,
                "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/42?brandSilo=WPAC",
                HttpMethod.PUT,
                headers,
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/orchestration-tracking-create-request.json",
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/orchestration/not-found-response.json", // Update endpoint returns no content
                HttpStatus.NOT_FOUND);
    }

}

