package au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.repository;

import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.db.entity.CFRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import regression.junit.IntegrationTestSupport;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;

@DataJpaTest
class CFRequestRepositoryTest {

    static {
        System.setProperty("gateway.host", "gw.dev1.api.srv.westpac.com.au");
    }

    @Autowired
    private CFRequestRepository repository;

    @Test
    @DisplayName("CRUD operations for FCFRequest using IntegrationTestSupport and JSON test data")
    void testCrud_withJsonTestData() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new com.fasterxml.jackson.datatype.jsr310.JavaTimeModule());

        // Load test data from JSON file
        CFRequest expected = IntegrationTestSupport.readObjectFromJson(
                "/au/com/westpac/card/lifecycle/event/fulfilment/v1/support/db/repository/cfrequest.json",
                CFRequest.class
        );

        // Create
        CFRequest saved = repository.save(objectMapper.readValue(objectMapper.writeValueAsString(expected), CFRequest.class));
        assertThat(saved.getId()).isNotNull();

        // Read and assert using jsonAssert
        Optional<CFRequest> found = repository.findById(saved.getId());
        assertThat(found).isPresent();

        // Set dynamic fields for expected
        expected.setId(saved.getId());
        String expectedJson = objectMapper.writeValueAsString(expected);
        String actualJson = objectMapper.writeValueAsString(found.get());
        JSONAssert.assertEquals(expectedJson, actualJson, JSONCompareMode.STRICT);

        // Update
        saved.setStatus("UPDATED");
        repository.save(saved);
        Optional<CFRequest> updated = repository.findById(saved.getId());
        assertThat(updated).isPresent();

        // Update expected object for comparison
        expected.setStatus("UPDATED");
        expectedJson = objectMapper.writeValueAsString(expected);
        actualJson = objectMapper.writeValueAsString(updated.get());
        JSONAssert.assertEquals(expectedJson, actualJson, JSONCompareMode.STRICT);

        // Delete
        repository.delete(saved);
        Optional<CFRequest> deleted = repository.findById(saved.getId());
        assertThat(deleted).isNotPresent();
    }
}