package au.com.westpac.card.lifecycle.event.fulfilment.v1.biz.healthcheck;


import au.com.westpac.card.lifecycle.event.fulfilment.v1.support.common.ControllerTestSupport;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.test.web.reactive.server.WebTestClient;

import java.io.IOException;
import java.net.URISyntaxException;

@SpringBootTest()
@AutoConfigureMockMvc
public class HealthCheckControllerTest {

  static {
    System.setProperty("gateway.host", "gw.dev1.api.srv.westpac.com.au");
  }

  @Test
  void testHealthCheckApiController(@Autowired WebTestClient webClient) throws URISyntaxException, IOException, JSONException {
    HttpHeaders headers = new HttpHeaders();
    ControllerTestSupport.callAndVerify(
        webClient,
        "/inf/card/lifecycle/event/fulfilment/v1/healthcheck",
        HttpMethod.GET,
        headers,
        null,
            "/au/com/westpac/card/lifecycle/event/fulfilment/v1/biz/healthcheck/health-check-response.json",
        HttpStatus.OK);
  }

}