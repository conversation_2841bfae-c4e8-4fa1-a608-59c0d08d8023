function() {
  var env = karate.env; // get system property 'karate.env'
  karate.log('karate.env system property was:', env);

  if (!env) {
    env = 'default';
  }

  env = 'sit1'

  var config = {
    urlBasePath: 'https://gw.sit1.api.srv.westpac.com.au',
//    urlBasePath: 'http://localhost:8080',
    apiKey: 'ec07a6fa-3a40-4630-bcb4-9389b0055a75',
    apiSecret: '947c7ac5-ff5c-439e-86b3-6e56af4fc8da',
    swimlane: '',
  };

  if (env == 'dev1') {
    config.urlBasePath = 'https://gw.dev1.api.srv.westpac.com.au';
  } else if (env == 'sit1') {
    config.urlBasePath = 'https://gw.sit1.api.srv.westpac.com.au';
  } else if (env == 'uat1') {
    config.urlBasePath = 'https://gw.uat1.api.srv.westpac.com.au';
  } else if (env == 'svp1') {
    config.urlBasePath = 'https://gw.svp1.api.srv.westpac.com.au';
  } else if (env == 'local') {
    config.urlBasePath = 'http://localhost:8080';
  } else if (env == 'default') {
    config.urlBasePath = 'http://localhost:8080';
  }

  karate.log('Config object:', config);

  karate.configure('logPrettyRequest', true);
  karate.configure('logPrettyResponse', true);

  return config;
}
