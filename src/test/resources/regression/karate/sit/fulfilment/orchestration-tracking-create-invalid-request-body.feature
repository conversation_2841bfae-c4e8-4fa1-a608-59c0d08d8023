Feature: Orchestration Tracking Create Invalid Request Body

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents?brandSilo=WPAC",
        "method": "POST",
        "description": "Create orchestration tracking event with invalid request body",
        "requestBodyObject": {
          "requestIdentifier": 123456789,
          "retryCount": 0,
          "parentWorkflowId": "aa02be8e-6536-11f0-99be-5a5b02a5396a",
          "workflowName": "card-creation-v1",
          "workflowId": "aa02be8e-6536-11f0-99be-5a5b02a5396a",
          "currentTask": "ValidateInput",
          "errorReason": "TimeoutError",
          "durationInMS": 1500
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-consumerType": "Staff",
          "x-originatingSystemId": "A00304",
          "x-messageId": "msg-12345"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1003,
                "message": "Invalid Body Parameter",
                "details": "The required status in body parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Orchestration Tracking Create Invalid Request Body
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }