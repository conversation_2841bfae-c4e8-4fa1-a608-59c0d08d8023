Feature: Fulfilment Request Search Ok

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequestSearch?brandSilo=WPAC",
        "method": "POST",
        "description": "Search fulfilment requests successfully",
        "requestBodyObject": {
          "type": "cardfacilityorigination",
          "paging": {
            "pageSize": 2,
            "nextPageKey": 2
          }
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-userId": "testuser123",
          "x-consumerType": "Staff",
          "x-messageId": "msg-12345",
          "x-originatingSystemId": "A00304"
        },
        "expectedHttpStatus": 200,
        "expectedResponseBody": {
          "data": {
            "requests": "#[_ > 0]",
            "paging": {"count":#number,"pageSize":2,"nextPageKey":3}
          }
        }
      }
      """

  Scenario: Fulfilment Request Search Ok
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }