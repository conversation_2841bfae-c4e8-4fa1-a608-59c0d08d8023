Feature: Orchestration Tracking Create Empty App Correlation Id

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents?brandSilo=WPAC",
        "method": "POST",
        "description": "Create orchestration tracking event with empty app correlation id",
        "requestBodyObject": {
          "eventType": "START",
          "workflowId": "wf-12345"
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "",
          "x-organisationId": "WPAC",
          "x-consumerType": "Staff",
          "x-originatingSystemId": "OSID",
          "x-messageId": "msg-12345"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1001,
                "message": "Invalid Header Parameter",
                "details": "The required xAppCorrelationId in header parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Orchestration Tracking Create Empty App Correlation Id
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }