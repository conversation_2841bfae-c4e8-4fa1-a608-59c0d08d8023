Feature: Fulfilment Request Update Empty Organisation Id

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/12345?brandSilo=WPAC",
        "method": "PUT",
        "description": "Update fulfilment request with empty organisation id",
        "requestBodyObject": {
          "type": "cardfacilityorigination",
          "status": "CARD_ACCOUNT_CREATION_SUCCESS",
          "applicationReferenceNumber": "APP-REF-2024-001234",
          "customerDetails": [
            {
              "customerId": {
                "id": "CUST*********",
                "idScheme": "CIF"
              },
              "firstName": "John",
              "lastName": "Smith",
              "countryCode": "AU",
              "customerType": "INDIVIDUAL",
              "customerSinceDate": "2020-01-15",
              "bankBrand": "WBC"
            }
          ],
          "facilityDetails": {
            "facilityId": *********,
            "facilityName": "Westpac Credit Card Facility"
          }
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "",
          "x-userId": "testuser123",
          "x-consumerType": "Staff",
          "x-messageId": "msg-12345",
          "x-originatingSystemId": "A00304"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1001,
                "message": "Invalid Header Parameter",
                "details": "The required x-organisationId in header parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Fulfilment Request Update Empty Organisation Id
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }