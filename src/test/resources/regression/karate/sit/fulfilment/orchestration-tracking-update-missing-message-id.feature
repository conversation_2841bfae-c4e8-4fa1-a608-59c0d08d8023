Feature: Orchestration Tracking Update Missing Message Id

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/12345?brandSilo=WPAC",
        "method": "PUT",
        "description": "Attempt to update orchestration tracking event with missing x-messageId header.",
        "requestBodyObject": {
          "eventType": "UPDATE",
          "workflowId": "wf-12345"
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-consumerType": "Staff",
          "x-originatingSystemId": "A00304"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1001,
                "message": "Invalid Header Parameter",
                "details": "The required x-messageId in header parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Orchestration Tracking Update Missing Message Id
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }