Feature: Fulfilment Request Update Facility Origination

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/1?brandSilo=WPAC",
        "method": "PUT",
        "description": "Update fulfilment request with facility origination data",
        "requestBodyObject": {
          "type": "FACILITYORGINATION",
          "status": "FULFILMENTRQST_IDENTIFIER_CREATED",
          "previousStatus": null,
          "sourceChannel": "WL",
          "createdBy": null,
          "updatedBy": null,
          "applicationReferenceNumber": "2111112",
          "customers": [
            {
              "customerId": {
                "id": "***********",
                "idScheme": "CustomerInternalId"
              },
              "firstName": "DOWNS",
              "middleName": "",
              "lastName": "DFGHJ",
              "suffixName": "",
              "gender": "F",
              "dateOfBirth": "1994-06-15",
              "email": "<EMAIL>",
              "alternateEmail": null,
              "mobilePhoneNumber": null,
              "workPhoneNumber": null,
              "homePhoneNumber": null,
              "countryCode": "AU",
              "customerType": "INDIVIDUAL",
              "customerLinkType": "OWNER",
              "customerStatus": "A",
              "customerSinceDate": "2020-04-02",
              "bankBrand": "WPAC",
              "occupationCode": "OCC 057",
              "sicCode": null,
              "cards": [
                {
                  "cardNumber": "****************",
                  "cardExpiry": null,
                  "cardType": "DEBIT",
                  "cardSubType": null,
                  "cardSuffix": null,
                  "cardScheme": null,
                  "cardStatus": null,
                  "cardEmbossedName": null,
                  "cardActivationDate": null,
                  "cardCreationDateTime": null,
                  "plasticStockCode": null,
                  "logo": "516",
                  "cardCreationStatus": null
                }
              ]
            },
            {
              "customerId": {
                "id": "***********",
                "idScheme": "CustomerInternalId"
              },
              "firstName": null,
              "middleName": null,
              "lastName": null,
              "suffixName": null,
              "gender": null,
              "dateOfBirth": null,
              "email": null,
              "alternateEmail": null,
              "mobilePhoneNumber": null,
              "workPhoneNumber": null,
              "homePhoneNumber": null,
              "countryCode": null,
              "customerType": "INDIVIDUAL",
              "customerLinkType": "LINKEDCARDHOLDER",
              "customerStatus": null,
              "customerSinceDate": null,
              "bankBrand": "WPAC",
              "occupationCode": null,
              "sicCode": null,
              "cards": [
                {
                  "cardNumber": "****************",
                  "cardExpiry": null,
                  "cardType": "DEBIT",
                  "cardSubType": null,
                  "cardSuffix": null,
                  "cardScheme": null,
                  "cardStatus": null,
                  "cardEmbossedName": null,
                  "cardActivationDate": null,
                  "cardCreationDateTime": null,
                  "plasticStockCode": null,
                  "logo": "516",
                  "cardCreationStatus": null
                }
              ]
            }
          ],
          "facilityDetails": {
            "facilityId": "0000000000003377978",
            "facilityName": "HSNDB ORG",
            "facilityContactName": "HSNDB ORG",
            "issueCardMailingAddressType": "B",
            "reissueCardMailingAddressType": "B",
            "billingAccountNumber": null,
            "cardAdminBranchCode": "032002",
            "cardCollectionBranchCode": "032002",
            "chequeAccountNumber": "739600",
            "chequeAccountBSB": "033053",
            "addresses": [
              {
                "addressType": "FACILITY_ADDRESS",
                "addressRelationshipInternalKey": "1-***********",
                "addressLine1": "69",
                "addressLine2": "MARKET STREET",
                "addressLine3": null,
                "city": "Sydney",
                "state": "NSW",
                "countryCode": "AU",
                "postalCode": "2000"
              }
            ]
          }
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-messageId": "random",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-originatingSystemId": "A00304",
          "x-consumerType": "Staff"
        },
        "expectedHttpStatus": 204,
        "expectedResponseBody": ""
      }
      """

  Scenario: Fulfilment Request Update Facility Origination
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }
