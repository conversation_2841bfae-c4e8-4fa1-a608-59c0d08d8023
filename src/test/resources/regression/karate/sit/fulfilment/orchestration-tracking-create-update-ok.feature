Feature: Orchestration Tracking Create Update Ok

  Background:
    * def workflowId = java.util.UUID.randomUUID().toString()

    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents?brandSilo=WPAC",
        "method": "POST",
        "description": "Create orchestration tracking event successfully",
        "requestBodyObject": {
          "requestIdentifier": 1,
          "status": "COMPLETED",
          "retryCount": 0,
          "parentWorkflowId": "aa02be8e-6536-11f0-99be-5a5b02a5396a",
          "workflowName": "card-creation-v1",
          "workflowId": "#(workflowId)",
          "currentTask": "ValidateInput",
          "errorReason": "TimeoutError",
          "durationInMS": 1500
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-consumerType": "Staff",
          "x-originatingSystemId": "A00304",
          "x-messageId": "msg-12345"
        },
        "expectedHttpStatus": 201,
        "expectedResponseBody": {
          "data": {
            "workflowId": "#string"
          }
        }
      }
      """

  Scenario: Orchestration Tracking Create Update Ok
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }

    * def updateOrchestrationBaseUrl = '/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents'
    * def fullUrl = updateOrchestrationBaseUrl + '/' + workflowId + '?brandSilo=WPAC'
    * print fullUrl


    * def testDataForUpdate =
      """
      {
        "url": "#(fullUrl)",
        "method": "PUT",
        "description": "Update orchestration tracking event successfully",
        "requestBodyObject":{
          "requestIdentifier": 1,
          "status": "COMPLETED",
          "retryCount": 0,
          "parentWorkflowId": "aa02be8e-6536-11f0-99be-5a5b02a5396a",
          "workflowName": "card-creation-v1",
          "workflowId": "#(workflowId)",
          "currentTask": "ValidateInputUpdated",
          "errorReason": "TimeoutError",
          "durationInMS": 1500
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-consumerType": "Staff",
          "x-originatingSystemId": "A00304",
          "x-messageId": "msg-12345"
        },
        "expectedHttpStatus": 204,
        "expectedResponseBody": ""
      }
      """
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testDataForUpdate) }