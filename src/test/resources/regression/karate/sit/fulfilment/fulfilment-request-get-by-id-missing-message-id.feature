Feature: Fulfilment Request Get By Id Missing Message Id

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/1?brandSilo=WPAC",
        "method": "GET",
        "description": "Get fulfilment request with missing message ID",
        "requestBodyObject": null,
        "headers": {
          "Accept": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-originatingSystemId": "A00304",
          "x-consumerType": "Staff"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1001,
                "message": "Invalid Header Parameter",
                "details": "The required x-messageId in header parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Fulfilment Request Get By Id Missing Message Id
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }
