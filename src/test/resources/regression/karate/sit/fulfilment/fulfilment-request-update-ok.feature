Feature: Fulfilment Request Update Ok

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/1?brandSilo=WPAC",
        "method": "PUT",
        "description": "Update fulfilment request successfully",
        "requestBodyObject": {
          "type": "cardfacilityorigination",
          "status": "CARD_ACCOUNT_CREATION_SUCCESS",
          "previousStatus": "FACILITY_NUMBER_CREATED",
          "sourceChannel": "MEDB",
          "createdBy": "APP_ID",
          "updatedBy": "APP_ID",
          "applicationReferenceNumber": "#(java.util.UUID.randomUUID().toString())",
          "customers": [
            {
              "customerId": {
                "id": "********",
                "idScheme": "CustomerInternalId"
              },
              "firstName": "UpdatedJohn",
              "middleName": "<PERSON>",
              "lastName": "<PERSON>",
              "suffixName": "Jr",
              "gender": "M",
              "dateOfBirth": "1985-03-15",
              "email": "<EMAIL>",
              "alternateEmail": "<EMAIL>",
              "mobilePhoneNumber": "+614********",
              "workPhoneNumber": "+***********",
              "homePhoneNumber": "+***********",
              "countryCode": "AU",
              "customerType": "INDIVIDUAL",
              "customerLinkType": "OWNER",
              "customerStatus": "ACTIVE",
              "customerSinceDate": "2020-01-15",
              "bankBrand": "WBC",
              "occupationCode": "ENG001",
              "sicCode": "7372",
              "cards": [
                {
                  "cardNumber": "****************",
                  "cardExpiry": "12/26",
                  "cardType": "CREDIT",
                  "cardSubType": "B",
                  "cardSuffix": "001",
                  "cardScheme": "VISA",
                  "cardStatus": "ACTIVE",
                  "cardEmbossedName": "Updated JOHN M SMITH",
                  "cardActivationDate": "2024-01-15",
                  "cardCreationDateTime": "2024-01-10T14:20:00Z",
                  "plasticStockCode": "PSC-VISA-GOLD-001",
                  "logo": "WBC",
                  "cardCreationStatus": "CARD_ACCOUNT_CREATION_SUCCESS"
                }
              ]
            }
          ],
          "facilityDetails": {
            "facilityId": "********9",
            "facilityName": "Westpac Credit Card Facility",
            "facilityContactName": "John Smith",
            "issueCardMailingAddressType": "HOME",
            "reissueCardMailingAddressType": "WORK",
            "billingAccountNumber": "UP-BILL-2024-001",
            "cardAdminBranchCode": "032001",
            "cardCollectionBranchCode": "032002",
            "chequeAccountNumber": "****************",
            "chequeAccountBSB": "032001",
            "addresses": [
              {
                "addressType": "RESIDENTIAL",
                "addressRelationshipInternalKey": "ADDR-REL-001",
                "addressLine1": "Updated 123 Collins Street",
                "addressLine2": "Level 15",
                "addressLine3": "Suite 1501",
                "city": "Melbourne",
                "state": "VIC",
                "countryCode": "AU",
                "postalCode": "3000"
              }
            ]
          }
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-messageId": "random",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-originatingSystemId": "A00304",
          "x-consumerType": "Staff"
        },
        "expectedHttpStatus": 204,
        "expectedResponseBody": ""
      }
      """

  Scenario: Fulfilment Request Update Ok
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }
