Feature: Orchestration Tracking Update Id Not Provided

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/?brandSilo=WPAC",
        "method": "PUT",
        "description": "Update orchestration tracking not providing id",
        "requestBodyObject": {
          "requestIdentifier": 1,
          "status": "COMPLETED",
          "retryCount": 0,
          "parentWorkflowId": "aa02be8e-6536-11f0-99be-5a5b02a5396a",
          "workflowName": "card-creation-v1",
          "workflowId": "aa02be8e-6536-11f0-99be-5a5b02a5396a",
          "currentTask": "ValidateInput",
          "errorReason": "TimeoutError",
          "durationInMS": 1500
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-consumerType": "Staff",
          "x-originatingSystemId": "A00304",
          "x-messageId": "msg-12345"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1005,
                "message": "Invalid Path Parameter",
                "details": "The required id in Path parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Orchestration Tracking Update Id Not Provided
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }
