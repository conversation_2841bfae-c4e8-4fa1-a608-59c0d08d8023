Feature: Fulfilment Request Create Invalid Request Body Type

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests?brandSilo=WPAC",
        "method": "POST",
        "description": "Create fulfilment request with invalid request body",
        "requestBodyObject": {
          "type": null,
          "status": "CARD_ACCOUNT_CREATION_SUCCESS",
          "previousStatus": "FACILITY_NUMBER_CREATED",
          "sourceChannel": "MEDB",
          "createdBy": "APP_ID",
          "updatedBy": "STAFF_001",
          "applicationReferenceNumber": "*********",
          "customers": [
            {
              "customerId": {
                "id": "********",
                "idScheme": "CustomerInternalId"
              },
              "firstName": "John",
              "middleName": "<PERSON>",
              "lastName": "Smith",
              "suffixName": "Jr",
              "gender": "M",
              "dateOfBirth": "1985-03-15",
              "email": "<EMAIL>",
              "alternateEmail": "<EMAIL>",
              "mobilePhoneNumber": "+614********",
              "workPhoneNumber": "+***********",
              "homePhoneNumber": "+***********",
              "countryCode": "AU",
              "customerType": "INDIVIDUAL",
              "customerLinkType": "OWNER",
              "customerStatus": "ACTIVE",
              "customerSinceDate": "2020-01-15",
              "bankBrand": "WBC",
              "occupationCode": "ENG001",
              "sicCode": "7372",
              "cards": [
                {
                  "cardNumber": "****************",
                  "cardExpiry": "12/26",
                  "cardType": "CREDIT",
                  "cardSubType": "B",
                  "cardSuffix": "001",
                  "cardScheme": "VISA",
                  "cardStatus": "ACTIVE",
                  "cardEmbossedName": "JOHN M SMITH",
                  "cardActivationDate": "2024-01-15T09:30:00Z",
                  "cardCreationDateTime": "2024-01-10T14:20:00Z",
                  "plasticStockCode": "PSCVISAGOLD",
                  "logo": "WBC",
                  "cardCreationStatus": "CARD_ACCOUNT_CREATION_SUCCESS"
                }
              ]
            }
          ],
          "facilityDetails": {
            "facilityId": "********9",
            "facilityName": "Westpac Credit Card Facility",
            "facilityContactName": "John Smith",
            "issueCardMailingAddressType": "HOME",
            "reissueCardMailingAddressType": "WORK",
            "billingAccountNumber": "2024001",
            "cardAdminBranchCode": "032001",
            "cardCollectionBranchCode": "032002",
            "chequeAccountNumber": "****************",
            "chequeAccountBSB": "032001",
            "addresses": [
              {
                "addressType": "facilityAddress",
                "addressRelationshipInternalKey": "ADDR-REL-001",
                "addressLine1": "123 Collins Street",
                "addressLine2": "Level 15",
                "addressLine3": "Suite 1501",
                "city": "Melbourne",
                "state": "VIC",
                "countryCode": "AU",
                "postalCode": "3000"
              }
            ]
          }
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-messageId": "msg-12345",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-userId": "testuser123",
          "x-consumerType": "Staff",
          "x-originatingSystemId": "A00304"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1003,
                "message": "Invalid Body Parameter",
                "details": "#string"
              }
            ]
          }
        }
      }
      """

  Scenario: Fulfilment Request Create Invalid Request Body Type
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }
