Feature: Fulfilment Request Search Empty Brand Silo

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequestSearch?brandSilo=",
        "method": "POST",
        "description": "Search fulfilment requests with empty brandSilo query parameter",
        "requestBodyObject": {
          "applicationReferenceNumber": "APP-REF-2024-001234",
          "status": "CARD_ACCOUNT_CREATION_SUCCESS"
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-userId": "testuser123",
          "x-consumerType": "Staff",
          "x-messageId": "msg-12345",
          "x-originatingSystemId": "A00304"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1002,
                "message": "Invalid Query Parameter",
                "details": "The required brandSilo in query parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Fulfilment Request Search Empty Brand Silo
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }