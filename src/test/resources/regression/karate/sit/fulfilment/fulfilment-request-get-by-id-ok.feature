Feature: Fulfilment Request Get By Id Ok

  Background:
    # Define the request ID as a variable (can be overridden from calling feature)
    * def id = 1

    * def baseUrl = '/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests'
    * def fullUrl = baseUrl + '/' + id + '?brandSilo=WPAC'

    * def testData =
      """
      {
        "url": "#(fullUrl)",
        "method": "GET",
        "description": "Get fulfilment request by ID successfully",
        "requestBodyObject": null,
        "headers": {
          "Accept": "application/json",
          "x-messageId": "random",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-originatingSystemId": "A00304",
          "x-consumerType": "Staff"
        },
        "expectedHttpStatus": 200,
        "expectedResponseBody": {
          "data": "#object"
        }
      }
      """

  Scenario: Fulfilment Request Get By Id Ok
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }
