Feature: Orchestration Tracking Create Invalid Workflow Name and ID

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents?brandSilo=WPAC",
        "method": "POST",
        "description": "Create orchestration tracking event with invalid workflowName and workflowId",
        "requestBodyObject": {
          "requestIdentifier": "43",
          "retryCount": "4",
          "status": "INPROGRESS",
          "workflowName": "longerThan200-abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHasdfsdafsdafsadfasdfdfsdfsdfdsIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcd114",
          "workflowId": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcd114@1"
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "31225364821331409190909",
          "x-organisationId": "WPAC",
          "x-consumerType": "Partner",
          "x-originatingSystemId": "A015B2",
          "x-messageId": "656984256365241",
          "Authorization": "Basic YWFmZDBjNmEtYjA3Mi00MDM5LThlNWYtYWFkMmQ5Njk4NTQ2OmJiMjEzYjkxLTIyOWQtNDE5Yy04Mjk0LWIxODg3NzliZmIxNQ=="
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1003,
                "message": "Invalid Body Parameter",
                "details": "The required workflowName in body parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Orchestration Tracking Create Invalid Workflow Name and ID
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }