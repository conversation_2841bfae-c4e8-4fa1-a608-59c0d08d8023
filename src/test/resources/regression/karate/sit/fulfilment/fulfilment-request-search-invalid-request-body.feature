Feature: Fulfilment Request Search Invalid Request Body

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequestSearch?brandSilo=WPAC",
        "method": "POST",
        "description": "Search fulfilment requests with invalid request body",
        "requestBodyObject": {
          "applicationReferenceNumber": "",
          "status": ""
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-userId": "testuser123",
          "x-consumerType": "Staff",
          "x-messageId": "msg-12345",
          "x-originatingSystemId": "A00304"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1003,
                "message": "Invalid Body Parameter",
                "details": "The required type in body parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Fulfilment Request Search Invalid Request Body
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }