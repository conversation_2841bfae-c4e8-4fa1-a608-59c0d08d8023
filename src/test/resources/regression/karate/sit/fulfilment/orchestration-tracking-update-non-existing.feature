Feature: Orchestration Tracking Update Non Existing

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/invalidId?brandSilo=WPAC",
        "method": "PUT",
        "description": "Attempt to update orchestration tracking event with a non-existing workflowInstanceId.",
        "requestBodyObject": {
          "requestIdentifier": 1,
          "status": "COMPLETED",
          "retryCount": 0,
          "parentWorkflowId": "aa02be8e-6536-11f0-99be-5a5b02a5396a",
          "workflowName": "card-creation-v1",
          "workflowId": "aa02be8e-6536-11f0-99be-5a5b02a5396a",
          "currentTask": "ValidateInput",
          "errorReason": "TimeoutError",
          "durationInMS": 1500
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-consumerType": "Staff",
          "x-originatingSystemId": "A00304",
          "x-messageId": "msg-12345"
        },
        "expectedHttpStatus": 404,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 4005,
                "message": "OrchestrationTrackingEvent not found",
                "details": "OrchestrationTrackingEvent not found for the provided workflowInstanceId"
              }
            ]
          }
        }
      }
      """

  Scenario: Orchestration Tracking Update Non Existing
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }
