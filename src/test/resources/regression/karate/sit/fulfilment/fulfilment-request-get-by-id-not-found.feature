Feature: Fulfilment Request Get By Id Not Found

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/999999?brandSilo=WPAC",
        "method": "GET",
        "description": "Get fulfilment request by non-existent ID",
        "requestBodyObject": null,
        "headers": {
          "Accept": "application/json",
          "x-messageId": "random",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-originatingSystemId": "A00304",
          "x-consumerType": "Staff"
        },
        "expectedHttpStatus": 404,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 4005,
                "message": "Fulfilment request not found",
                "details": "Fulfilment request was not found for the provide requestid"
              }
            ]
          }
        }
      }
      """

  Scenario: Fulfilment Request Get By Id Not Found
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }
