Feature: Fulfilment Request Search Missing Originating System Id

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequestSearch?brandSilo=WPAC",
        "method": "POST",
        "description": "Search fulfilment requests with missing originating system id",
        "requestBodyObject": {
          "applicationReferenceNumber": "APP-REF-2024-001234",
          "status": "CARD_ACCOUNT_CREATION_SUCCESS"
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-userId": "testuser123",
          "x-consumerType": "Staff",
          "x-messageId": "msg-12345"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1001,
                "message": "Invalid Header Parameter",
                "details": "The required x-originatingSystemId in header parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Fulfilment Request Search Missing Originating System Id
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }