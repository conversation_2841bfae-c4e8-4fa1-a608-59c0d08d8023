Feature: Fulfilment Request Search Empty Consumer Type

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequestSearch?brandSilo=WPAC",
        "method": "POST",
        "description": "Search fulfilment requests with empty consumer type",
        "requestBodyObject": {
          "applicationReferenceNumber": "APP-REF-2024-001234",
          "status": "CARD_ACCOUNT_CREATION_SUCCESS"
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-userId": "testuser123",
          "x-consumerType": "",
          "x-messageId": "msg-12345",
          "x-originatingSystemId": "A00304"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1001,
                "message": "Invalid Header Parameter",
                "details": "The required x-consumerType in header parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Fulfilment Request Search Empty Consumer Type
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }