Feature: Fulfilment Request Create Get Update Get

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests?brandSilo=WPAC",
        "method": "POST",
        "description": "Create fulfilment request successfully",
        "requestBodyObject": {
          "type": "cardfacilityorigination",
          "status": "CARD_ACCOUNT_CREATION_SUCCESS",
          "previousStatus": "FACILITY_NUMBER_CREATED",
          "sourceChannel": "MEDB",
          "createdBy": "APP_ID",
          "updatedBy": "APP_ID",
          "applicationReferenceNumber": "#(java.util.UUID.randomUUID().toString())",
          "customers": [
            {
              "customerId": {
                "id": "********",
                "idScheme": "CustomerInternalId"
              },
              "firstName": "<PERSON>",
              "middleName": "<PERSON>",
              "lastName": "<PERSON>",
              "suffixName": "Jr",
              "gender": "M",
              "dateOfBirth": "1985-03-15",
              "email": "<EMAIL>",
              "alternateEmail": "<EMAIL>",
              "mobilePhoneNumber": "+614********",
              "workPhoneNumber": "+***********",
              "homePhoneNumber": "+***********",
              "countryCode": "AU",
              "customerType": "INDIVIDUAL",
              "customerLinkType": "OWNER",
              "customerStatus": "ACTIVE",
              "customerSinceDate": "2020-01-15",
              "bankBrand": "WBC",
              "occupationCode": "ENG001",
              "sicCode": "7372",
              "cards": [
                {
                  "cardNumber": "****************",
                  "cardExpiry": "12/26",
                  "cardType": "CREDIT",
                  "cardSubType": "B",
                  "cardSuffix": "001",
                  "cardScheme": "VISA",
                  "cardStatus": "ACTIVE",
                  "cardEmbossedName": "JOHN M SMITH",
                  "cardActivationDate": "2024-01-15",
                  "cardCreationDateTime": "2024-07-24T23:23:23+10:00",
                  "plasticStockCode": "PSC-VISA-GOLD-001",
                  "logo": "WBC",
                  "cardCreationStatus": "CARD_ACCOUNT_CREATION_SUCCESS"
                }
              ]
            }
          ],
          "facilityDetails": {
            "facilityId": "********9",
            "facilityName": "Westpac Credit Card Facility",
            "facilityContactName": "John Smith",
            "issueCardMailingAddressType": "HOME",
            "reissueCardMailingAddressType": "WORK",
            "billingAccountNumber": "ACC-BILL-2024-001",
            "cardAdminBranchCode": "032001",
            "cardCollectionBranchCode": "032002",
            "chequeAccountNumber": "****************",
            "chequeAccountBSB": "032001",
            "addresses": [
              {
                "addressType": "RESIDENTIAL",
                "addressRelationshipInternalKey": "ADDR-REL-001",
                "addressLine1": "123 Collins Street",
                "addressLine2": "Level 15",
                "addressLine3": "Suite 1501",
                "city": "Melbourne",
                "state": "VIC",
                "countryCode": "AU",
                "postalCode": "3000"
              }
            ]
          }
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-messageId": "msg-12345",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-userId": "testuser123",
          "x-consumerType": "Staff",
          "x-originatingSystemId": "A00304"
        },
        "expectedHttpStatus": 201,
        "expectedResponseBody": {
          "data": {
            "requestId": "#string"
          }
        }
      }
      """

  Scenario: Fulfilment Request Create Get Update Get
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }

    * print response

    * def id = response.data.requestId

    * def getFulfilmentRequestByIdBaseUrl = '/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests'
    * def fullUrl = getFulfilmentRequestByIdBaseUrl + '/' + id + '?brandSilo=WPAC'
    * print fullUrl

    * def testDataForGet =
      """
      {
        "url": "#(fullUrl)",
        "method": "GET",
        "description": "Get fulfilment request by ID successfully",
        "requestBodyObject": null,
        "headers": {
          "Accept": "application/json",
          "x-messageId": "random",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-originatingSystemId": "A00304",
          "x-consumerType": "Staff"
        },
        "expectedHttpStatus": 200,
        "expectedResponseBody": {
          "data": {
            "requestId": "#string",
            "type": "cardfacilityorigination",
            "status": "CARD_ACCOUNT_CREATION_SUCCESS",
            "previousStatus": "FACILITY_NUMBER_CREATED",
            "sourceChannel": "MEDB",
            "createdDateTime": "#string",
            "createdBy": "APP_ID",
            "updatedDateTime": "#string",
            "updatedBy": "APP_ID",
            "applicationReferenceNumber": "#string",
            "customers": [
              {
                "customerId": {
                  "id": "********",
                  "idScheme": "CustomerInternalId"
                },
                "firstName": "John",
                "middleName": "Michael",
                "lastName": "Smith",
                "suffixName": "Jr",
                "gender": "M",
                "dateOfBirth": "1985-03-15",
                "email": "<EMAIL>",
                "alternateEmail": "<EMAIL>",
                "mobilePhoneNumber": "+614********",
                "workPhoneNumber": "+***********",
                "homePhoneNumber": "+***********",
                "countryCode": "AU",
                "customerType": "INDIVIDUAL",
                "customerLinkType": "OWNER",
                "customerStatus": "ACTIVE",
                "customerSinceDate": "#string",
                "bankBrand": "WBC",
                "occupationCode": "ENG001",
                "sicCode": "7372",
                "cards": [
                  {
                    "cardNumber": "****************",
                    "cardExpiry": "12/26",
                    "cardType": "CREDIT",
                    "cardSubType": "B",
                    "cardSuffix": "001",
                    "cardScheme": "VISA",
                    "cardStatus": "ACTIVE",
                    "cardEmbossedName": "JOHN M SMITH",
                    "cardActivationDate": "2024-01-15",
                    "cardCreationDateTime": "2024-07-24T23:23:23+10:00",
                    "plasticStockCode": "PSC-VISA-GOLD-001",
                    "logo": "WBC",
                    "cardCreationStatus": "CARD_ACCOUNT_CREATION_SUCCESS"
                  }
                ]
              }
            ],
            "facilityDetails": {
              "facilityId": "********9",
              "facilityName": "Westpac Credit Card Facility",
              "facilityContactName": "John Smith",
              "issueCardMailingAddressType": "HOME",
              "reissueCardMailingAddressType": "WORK",
              "billingAccountNumber": "ACC-BILL-2024-001",
              "cardAdminBranchCode": "032001",
              "cardCollectionBranchCode": "032002",
              "chequeAccountNumber": "****************",
              "chequeAccountBSB": "032001",
              "addresses": [
                {
                  "addressType": "RESIDENTIAL",
                  "addressRelationshipInternalKey": "ADDR-REL-001",
                  "addressLine1": "123 Collins Street",
                  "addressLine2": "Level 15",
                  "addressLine3": "Suite 1501",
                  "city": "Melbourne",
                  "state": "VIC",
                  "countryCode": "AU",
                  "postalCode": "3000"
                }
              ]
            }
          }
        }
      }
      """

    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testDataForGet) }

    * def testDataForUpdate =
      """
      {
        "url": "#(fullUrl)",
        "method": "put",
        "description": "Update fulfilment request successfully",
        "requestBodyObject": {
          "type": "cardfacilityorigination",
          "status": "CARD_ACCOUNT_CREATION_SUCCESS",
          "previousStatus": "FACILITY_NUMBER_CREATED",
          "sourceChannel": "MEDB",
          "createdBy": "APP_ID",
          "updatedBy": "APP_ID",
          "applicationReferenceNumber": "#(java.util.UUID.randomUUID().toString())",
          "customers": [
            {
              "customerId": {
                "id": "********",
                "idScheme": "CustomerInternalId"
              },
              "firstName": "UpdatedJohn",
              "middleName": "Michael",
              "lastName": "Smith",
              "suffixName": "Jr",
              "gender": "M",
              "dateOfBirth": "1985-03-15",
              "email": "<EMAIL>",
              "alternateEmail": "<EMAIL>",
              "mobilePhoneNumber": "+614********",
              "workPhoneNumber": "+***********",
              "homePhoneNumber": "+***********",
              "countryCode": "AU",
              "customerType": "INDIVIDUAL",
              "customerLinkType": "OWNER",
              "customerStatus": "ACTIVE",
              "customerSinceDate": "2020-01-15",
              "bankBrand": "WBC",
              "occupationCode": "ENG001",
              "sicCode": "7372",
              "cards": [
                {
                  "cardNumber": "****************",
                  "cardExpiry": "12/26",
                  "cardType": "CREDIT",
                  "cardSubType": "B",
                  "cardSuffix": "001",
                  "cardScheme": "VISA",
                  "cardStatus": "ACTIVE",
                  "cardEmbossedName": "Updated JOHN M SMITH",
                  "cardActivationDate": "2024-01-15",
                  "cardCreationDateTime": "2024-07-24T23:23:23+10:00",
                  "plasticStockCode": "PSC-VISA-GOLD-001",
                  "logo": "WBC",
                  "cardCreationStatus": "CARD_ACCOUNT_CREATION_SUCCESS"
                }
              ]
            }
          ],
          "facilityDetails": {
            "facilityId": "********9",
            "facilityName": "Westpac Credit Card Facility",
            "facilityContactName": "John Smith",
            "issueCardMailingAddressType": "HOME",
            "reissueCardMailingAddressType": "WORK",
            "billingAccountNumber": "UP-BILL-2024-001",
            "cardAdminBranchCode": "032001",
            "cardCollectionBranchCode": "032002",
            "chequeAccountNumber": "****************",
            "chequeAccountBSB": "032001",
            "addresses": [
              {
                "addressType": "RESIDENTIAL",
                "addressRelationshipInternalKey": "ADDR-REL-001",
                "addressLine1": "Updated 123 Collins Street",
                "addressLine2": "Level 15",
                "addressLine3": "Suite 1501",
                "city": "Melbourne",
                "state": "VIC",
                "countryCode": "AU",
                "postalCode": "3000"
              }
            ]
          }
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-messageId": "msg-12345",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-userId": "testuser123",
          "x-consumerType": "Staff",
          "x-originatingSystemId": "A00304"
        },
        "expectedHttpStatus": 204,
        "expectedResponseBody": ""
      }
      """
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testDataForUpdate) }


    * def testDataForGet =
      """
      {
        "url": "#(fullUrl)",
        "method": "GET",
        "description": "Get fulfilment request by ID successfully",
        "requestBodyObject": null,
        "headers": {
          "Accept": "application/json",
          "x-messageId": "random",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-originatingSystemId": "A00304",
          "x-consumerType": "Staff"
        },
        "expectedHttpStatus": 200,
        "expectedResponseBody": {
          "data": {
            "requestId": "#string",
            "type": "cardfacilityorigination",
            "status": "CARD_ACCOUNT_CREATION_SUCCESS",
            "previousStatus": "FACILITY_NUMBER_CREATED",
            "sourceChannel": "MEDB",
            "createdDateTime": "#string",
            "createdBy": "APP_ID",
            "updatedDateTime": "#string",
            "updatedBy": "APP_ID",
            "applicationReferenceNumber": "#string",
            "customers": [
              {
                "customerId": {
                  "id": "********",
                  "idScheme": "CustomerInternalId"
                },
                "firstName": "UpdatedJohn",
                "middleName": "Michael",
                "lastName": "Smith",
                "suffixName": "Jr",
                "gender": "M",
                "dateOfBirth": "1985-03-15",
                "email": "<EMAIL>",
                "alternateEmail": "<EMAIL>",
                "mobilePhoneNumber": "+614********",
                "workPhoneNumber": "+***********",
                "homePhoneNumber": "+***********",
                "countryCode": "AU",
                "customerType": "INDIVIDUAL",
                "customerLinkType": "OWNER",
                "customerStatus": "ACTIVE",
                "customerSinceDate": "#string",
                "bankBrand": "WBC",
                "occupationCode": "ENG001",
                "sicCode": "7372",
                "cards": [
                  {
                    "cardNumber": "****************",
                    "cardExpiry": "12/26",
                    "cardType": "CREDIT",
                    "cardSubType": "B",
                    "cardSuffix": "001",
                    "cardScheme": "VISA",
                    "cardStatus": "ACTIVE",
                    "cardEmbossedName": "Updated JOHN M SMITH",
                    "cardActivationDate": "2024-01-15",
                    "cardCreationDateTime": "2024-07-24T23:23:23+10:00",
                    "plasticStockCode": "PSC-VISA-GOLD-001",
                    "logo": "WBC",
                    "cardCreationStatus": "CARD_ACCOUNT_CREATION_SUCCESS"
                  }
                ]
              }
            ],
            "facilityDetails": {
              "facilityId": "********9",
              "facilityName": "Westpac Credit Card Facility",
              "facilityContactName": "John Smith",
              "issueCardMailingAddressType": "HOME",
              "reissueCardMailingAddressType": "WORK",
              "billingAccountNumber": "UP-BILL-2024-001",
              "cardAdminBranchCode": "032001",
              "cardCollectionBranchCode": "032002",
              "chequeAccountNumber": "****************",
              "chequeAccountBSB": "032001",
              "addresses": [
                {
                  "addressType": "RESIDENTIAL",
                  "addressRelationshipInternalKey": "ADDR-REL-001",
                  "addressLine1": "Updated 123 Collins Street",
                  "addressLine2": "Level 15",
                  "addressLine3": "Suite 1501",
                  "city": "Melbourne",
                  "state": "VIC",
                  "countryCode": "AU",
                  "postalCode": "3000"
                }
              ]
            }
          }
        }
      }
      """

    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testDataForGet) }
