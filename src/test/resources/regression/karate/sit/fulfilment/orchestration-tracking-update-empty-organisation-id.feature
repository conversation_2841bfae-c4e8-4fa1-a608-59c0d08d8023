Feature: Orchestration Tracking Update Empty Organisation Id

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/track-12345?brandSilo=WPAC",
        "method": "PUT",
        "description": "Update orchestration tracking event with empty organisation id",
        "requestBodyObject": {
          "eventType": "UPDATE",
          "workflowId": "wf-12345"
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "",
          "x-consumerType": "Staff",
          "x-originatingSystemId": "OSID",
          "x-messageId": "msg-12345"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1001,
                "message": "Invalid Header Parameter",
                "details": "The required x-organisationId in header parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Orchestration Tracking Update Empty Organisation Id
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }