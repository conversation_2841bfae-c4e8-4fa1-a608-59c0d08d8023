Feature: Orchestration Tracking Update Empty Consumer Type

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents/track-12345?brandSilo=WPAC",
        "method": "PUT",
        "description": "Update orchestration tracking event with empty consumer type",
        "requestBodyObject": {
          "eventType": "UPDATE",
          "workflowId": "wf-12345"
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-consumerType": "",
          "x-originatingSystemId": "OSID",
          "x-messageId": "msg-12345"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1001,
                "message": "Invalid Header Parameter",
                "details": "The required x-consumerType in header parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Orchestration Tracking Update Empty Consumer Type
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }