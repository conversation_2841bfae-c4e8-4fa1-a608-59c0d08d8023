Feature: Orchestration Tracking Create Missing Brand Silo

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/orchestrationTrackingEvents",
        "method": "POST",
        "description": "Create orchestration tracking event with missing brandSilo query parameter",
        "requestBodyObject": {
          "eventType": "START",
          "workflowId": "wf-12345"
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-consumerType": "Staff",
          "x-originatingSystemId": "A00304",
          "x-messageId": "msg-12345"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1002,
                "message": "Invalid Query Parameter",
                "details": "The required brandSilo in query parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Orchestration Tracking Create Missing Brand Silo
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }