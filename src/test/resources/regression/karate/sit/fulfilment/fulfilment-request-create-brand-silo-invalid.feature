Feature: Fulfilment Request Create Brand Silo Invalid

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests?brandSilo=INVALID",
        "method": "POST",
        "description": "Attempt to create a fulfilment request with an invalid or missing brandSilo query parameter.",
        "requestBodyObject": {
          "type": "cardfacilityorigination",
          "status": "CARD_ACCOUNT_CREATION_SUCCESS",
          "applicationReferenceNumber": "APP-REF-2024-001234",
          "customers": [
            {
              "customerId": {
                "id": "CUST*********",
                "idScheme": "CIF"
              },
              "firstName": "John",
              "lastName": "Smith",
              "countryCode": "AU",
              "customerType": "INDIVIDUAL",
              "customerSinceDate": "2020-01-15",
              "bankBrand": "WBC"
            }
          ],
          "facilityDetails": {
            "facilityId": *********,
            "facilityName": "Westpac Credit Card Facility"
          }
        },
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-userId": "testuser123",
          "x-consumerType": "Staff",
          "x-messageId": "msg-12345",
          "x-originatingSystemId": "A00304"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1002,
                "message": "Invalid Query Parameter",
                "details": "The required brandSilo in query parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Fulfilment Request Create Brand Silo Invalid
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }
