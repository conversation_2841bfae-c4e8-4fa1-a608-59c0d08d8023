Feature: Fulfilment Request Get Missing Correlation Id

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/12345?brandSilo=WPAC",
        "method": "GET",
        "description": "Get fulfilment request with missing correlation id",
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-organisationId": "WPAC",
          "x-userId": "testuser123",
          "x-consumerType": "Staff",
          "x-messageId": "msg-12345",
          "x-originatingSystemId": "A00304"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1001,
                "message": "Invalid Header Parameter",
                "details": "The required x-appCorrelationId in header parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Fulfilment Request Get Missing Correlation Id
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }