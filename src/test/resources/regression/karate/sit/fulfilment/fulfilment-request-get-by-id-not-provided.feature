Feature: Fulfilment Request Get By Id Not Provided

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/fulfilmentRequests/?brandSilo=WPAC",
        "method": "GET",
        "description": "Get fulfilment request by non-existent ID",
        "requestBodyObject": null,
        "headers": {
          "Accept": "application/json",
          "x-messageId": "random",
          "x-appCorrelationId": "random",
          "x-organisationId": "WPAC",
          "x-originatingSystemId": "A00304",
          "x-consumerType": "Staff"
        },
        "expectedHttpStatus": 400,
        "expectedResponseBody": {
          "result": {
            "errors": [
              {
                "code": 1005,
                "message": "Invalid Path Parameter",
                "details": "The required id in Path parameter is missing or invalid."
              }
            ]
          }
        }
      }
      """

  Scenario: Fulfilment Request Get By Id Not Provided
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }
