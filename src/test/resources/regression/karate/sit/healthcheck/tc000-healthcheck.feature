Feature: Health check

  Background:
    * def testData =
      """
      {
        "url": "/inf/card/lifecycle/event/fulfilment/v1/healthcheck",
        "method": "GET",
        "description": "API health check",
        "requestBodyObject":  {},
        "headers": {
          "Accept": "application/json",
          "Content-Type": "application/json",
          "x-messageId": "5436",
          "x-appCorrelationId": "111222-333444",
          "x-organisationId": "WPAC",
          "x-consumerType": "Customer",
          "x-originatingSystemId": "666666"
        },
        "expectedHttpStatus": 200,
        "expectedResponseBody": {"status":"success"}
      }
      """

  Scenario: Successful
    * call read('classpath:regression/karate/general/CommonComponent.feature') { data: #(testData) }
    * print response
