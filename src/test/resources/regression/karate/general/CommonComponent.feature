@ignore
Feature: Common component for executing requests and validating responses

  Scenario: Execute request and validate response
    * print 'data is" ', data
    * def methodToCall = data.method
    * def credentials = apiKey + ':' + apiSecret
    * def encodedCredentials = Java.type('java.util.Base64').encoder.encodeToString(credentials.getBytes())
    # Conditionally update x-appCorrelationId if it is 'random'
    * def uuid = java.util.UUID.randomUUID().toString()
    * if (data.headers['x-appCorrelationId'] == 'random') data.headers['x-appCorrelationId'] = uuid

    * print 'correlationId is: ', data.headers["x-appCorrelationId"]
    * header Authorization = 'Basic ' + encodedCredentials
    * if (typeof swimlane !== 'undefined') karate.configure('headers', { 'x-swimlane': swimlane })

    Given url urlBasePath + data.url
    * print 'baseURL is: ', urlBasePath + data.url
    And request data.requestBodyObject
    And headers data.headers
    When method methodToCall
    And print 'Response:', karate.pretty(response)
    * if (data.expectedResponseBody == null) data.expectedResponseBody = ''
    * print 'Expected response body is: ', data.expectedResponseBody
    And match responseStatus == data.expectedHttpStatus
    And match response == data.expectedResponseBody

    * def result = response
