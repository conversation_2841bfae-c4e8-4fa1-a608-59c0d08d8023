{"id": 1, "type": "cardactivation", "status": "COMPLETED", "previousStatus": "CARD_ACCOUNT_CREATION_SUCCESS", "sourceChannel": "MB", "createdDateTime": "2024-07-24T08:39:00+10:00", "createdBy": "SYSTEM_USER", "updatedDateTime": "2024-07-24T08:39:00+10:00", "updatedBy": "SYSTEM_USER", "applicationReferenceNumber": "APP-REF-2024-005678", "facilityDetail": {"facilityId": "*********", "facilityName": "St.George Credit Card Facility", "facilityContactName": "<PERSON>", "issueCardMailingAddressType": "WORK", "reissueCardMailingAddressType": "HOME", "billingAccountNumber": "ACC-BILL-2024-002", "cardAdminBranchCode": "032003", "cardCollectionBranchCode": "032004", "chequeAccountNumber": "****************", "chequeAccountBSB": "032003", "addressDetails": [{"addressType": "facilityAddress", "addressRelationshipInternalKey": "ADDR-REL-002", "addressLine1": "456 George Street", "addressLine2": "Level 10", "addressLine3": "Suite 1002", "city": "Sydney", "state": "NSW", "countryCode": "AU", "postalCode": "2000"}], "customerDetails": [{"customerId": "CUST*********", "customerIdScheme": "CustomerInternalId", "firstName": "<PERSON>", "middleName": "<PERSON>", "lastName": "<PERSON><PERSON>", "suffixName": "Ms", "gender": "F", "dateOfBirth": "1990-07-20", "email": "<EMAIL>", "alternateEmail": "<EMAIL>", "mobilePhoneNumber": "+***********", "workPhoneNumber": "+***********", "homePhoneNumber": "+***********", "countryCode": "AU", "customerType": "INDIVIDUAL", "customerLinkType": "OWNER", "customerStatus": "ACTIVE", "customerSinceDate": "2022-07-25", "bankBrand": "STG", "occupationCode": "MED001", "sicCode": "8011", "cardDetails": [{"cardNumber": "****************", "cardExpiry": "11/27", "cardType": "CREDIT", "cardSubType": "A", "cardSuffix": "002", "cardScheme": "MASTERCARD", "cardStatus": "ACTIVE", "cardEmbossedName": "JANE E DOE", "cardActivationDate": "2024-07-25", "cardCreationDateTime": "2024-07-24T23:23:23+10:00", "plasticStockCode": "PSCMCSILVER", "logo": "STG", "cardCreationStatus": "CARD_ACCOUNT_CREATION_SUCCESS"}]}]}}